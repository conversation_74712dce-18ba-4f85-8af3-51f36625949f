# -*- coding: utf-8 -*-
"""
ملف إدارة الموردين - نظام شامل ومتطور لإدارة بيانات الموردين
يحتوي على جميع الوظائف المطلوبة للبحث والتحليل والتقارير والتصدير
"""

# استيرادات PyQt5
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QLineEdit,
    QTableWidget, QTableWidgetItem, QMenu, QAction, QFrame, QSizePolicy,
    QDialog, QFileDialog, QMessageBox, QScrollArea, QListWidget,
    QListWidgetItem, QTextEdit, QFormLayout, QCheckBox, QTabWidget,
    QComboBox, QDoubleSpinBox, QDateEdit
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor

# استيرادات Python المعيارية
from datetime import datetime
import os
import json

# استيرادات المشروع
from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledLabel, BaseDialog
from database import Supplier, SupplierPhone, Expense, update_supplier_balance
from utils import (
    show_error_message, show_info_message, show_confirmation_message,
    is_valid_email, is_valid_phone, format_currency, format_date
)

class SuppliersWidget(QWidget):
    """
    واجهة إدارة الموردين المتطورة

    تحتوي على:
    - جدول الموردين مع جميع البيانات
    - أدوات البحث والفلترة المتقدمة
    - عمليات الإضافة والتعديل والحذف
    - التصدير والاستيراد بصيغ متعددة
    - النسخ الاحتياطي واستعادة البيانات
    - الإحصائيات والتقارير التفصيلية
    """

    def __init__(self, session):
        """
        تهيئة واجهة إدارة الموردين

        Args:
            session: جلسة قاعدة البيانات
        """
        super().__init__()
        self.session = session
        self.suppliers_table = None
        self.search_edit = None

        # تهيئة الواجهة وتحميل البيانات
        self.init_ui()
        self.refresh_data()

    # ==================== إعداد الواجهة الرئيسية ====================

    def init_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(3, 3, 3, 3)
        main_layout.setSpacing(3)

        # إضافة العنوان الرئيسي
        self._create_title_section(main_layout)

        # إضافة قسم البحث والأدوات
        self._create_search_section(main_layout)

        # إضافة الجدول الرئيسي
        self._create_table_section(main_layout)

        # إضافة قسم الأزرار في الأسفل
        self._create_buttons_section(main_layout)

        # تطبيق التخطيط
        self.setLayout(main_layout)

    def _create_title_section(self, main_layout):
        """إنشاء قسم العنوان الرئيسي"""
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة بيانات الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

    def _create_search_section(self, main_layout):
        """إنشاء قسم البحث والفلترة"""
        # إنشاء إطار البحث
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط البحث
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(2)

        # حاوي التوسيط العمودي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(3, 0, 3, 0)
        top_container.setSpacing(0)
        top_container.addStretch(1)
        top_container.addLayout(search_layout)
        top_container.addStretch(1)

        # تسمية البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b);
                border: 3px solid #7f1d1d;
                border-radius: 12px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c);
                border: 3px solid #ef4444;
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الهاتف، الإيميل أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0);
                border: 3px solid #4f46e5;
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: #4f46e5;
            }
            QLineEdit:focus {
                border: 3px solid #3730a3;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f9ff, stop:1 #e0f2fe);
            }
            QLineEdit:hover {
                border: 3px solid #5b52f0;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafbff, stop:1 #f1f5f9);
            }
        """)

        # زر البحث
        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75);
                color: #ffffff;
                border: 3px solid #164e63;
                border-radius: 12px;
                padding: 8px;
                font-size: 20px;
                font-weight: bold;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490);
                border: 3px solid #22d3ee;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0e7490, stop:1 #155e75);
                border: 3px solid #0e7490;
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        # إضافة العناصر للتخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_button)

        # إضافة أزرار التصفية السريعة
        self._add_filter_buttons(search_layout)

        # تطبيق التخطيط على الإطار
        top_frame.setLayout(top_container)
        main_layout.addWidget(top_frame)

    def _add_filter_buttons(self, search_layout):
        """إضافة أزرار التصفية السريعة"""
        # تسمية التصفية
        filter_label = QLabel("🎯 تصفية:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309);
                border: 3px solid #92400e;
                border-radius: 12px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706);
                border: 3px solid #fbbf24;
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)

        # أزرار التصفية
        filter_buttons = [
            ("📊 الكل", 'slate', lambda: self.filter_by_balance('all')),
            ("💰 لهم مبلغ", 'emerald', lambda: self.filter_by_balance('positive')),
            ("⚠️ عليهم مبلغ", 'danger', lambda: self.filter_by_balance('negative')),
            ("⚪ بدون رصيد", 'secondary', lambda: self.filter_by_balance('zero')),
            ("👁️ الأعمدة", 'purple', self.show_columns_dialog),
            ("📋 عمليات مجمعة", 'indigo', self.show_bulk_operations_dialog),
            ("🎯 فلاتر متقدمة", 'rose', self.show_advanced_filters_dialog)
        ]

        search_layout.addWidget(filter_label)
        for text, style, callback in filter_buttons:
            button = QPushButton(text)
            self.style_advanced_button(button, style)
            button.clicked.connect(callback)
            button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            search_layout.addWidget(button)

    def _create_buttons_section(self, main_layout):
        """إنشاء قسم الأزرار الرئيسية"""
        # إنشاء إطار الأزرار
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط الأزرار
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(2)

        # حاوي التوسيط العمودي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(3, 0, 3, 0)
        bottom_container.setSpacing(0)
        bottom_container.addStretch(1)
        bottom_container.addLayout(actions_layout)
        bottom_container.addStretch(1)

        # إنشاء الأزرار الرئيسية
        self._create_main_buttons(actions_layout)

        # تطبيق التخطيط
        bottom_frame.setLayout(bottom_container)
        main_layout.addWidget(bottom_frame)

    def _create_main_buttons(self, actions_layout):
        """إنشاء الأزرار الرئيسية"""
        # زر الإضافة مع قائمة منسدلة
        self.add_button = QPushButton("➕ إضافة مورد ▼")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة الإضافة
        add_menu = QMenu(self)
        add_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal'))

        # خيارات الإضافة
        add_actions = [
            ("🏪 إضافة مورد عادي", self.add_supplier),
            ("⚡ إضافة سريعة", self.quick_add_supplier),
            ("📥 استيراد من ملف", self.import_from_file)
        ]

        for i, (text, callback) in enumerate(add_actions):
            if i == 1:  # إضافة فاصل بعد الخيار الأول
                add_menu.addSeparator()
            action = QAction(text, self)
            action.triggered.connect(callback)
            add_menu.addAction(action)

        self.add_button.setMenu(add_menu)

        # الأزرار الأساسية
        basic_buttons = [
            ("✏️ تعديل", 'primary', self.edit_supplier),
            ("🗑️ حذف", 'danger', self.delete_supplier),
            ("🔄 تحديث", 'modern_teal', self.refresh_data),
            ("💰 تعديل الرصيد", 'orange', self.adjust_balance),
            ("📎 إضافة المرفقات", 'purple', self.manage_attachments)
        ]

        # إنشاء الأزرار الأساسية
        for text, style, callback in basic_buttons:
            button = QPushButton(text)
            self.style_advanced_button(button, style)
            button.clicked.connect(callback)
            button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            actions_layout.addWidget(button)

            # حفظ مراجع للأزرار المهمة
            if "تعديل" in text:
                self.edit_button = button
            elif "حذف" in text:
                self.delete_button = button
            elif "تحديث" in text:
                self.refresh_button = button

        # أزرار مع قوائم منسدلة
        self._create_menu_buttons(actions_layout)

        # إضافة زر الإضافة في البداية
        actions_layout.insertWidget(0, self.add_button)

    def _create_menu_buttons(self, actions_layout):
        """إنشاء الأزرار مع القوائم المنسدلة"""
        # زر التفاصيل
        self.details_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.details_button, 'indigo', has_menu=True)
        self.details_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        details_menu = QMenu(self)
        details_menu.setStyleSheet(UnifiedStyles.get_menu_style('primary', 'normal'))

        details_actions = [
            ("🏪 التفاصيل الأساسية", self.show_supplier_details),
            ("💰 التفاصيل المالية", self.show_financial_details),
            ("📞 تفاصيل الاتصال", self.show_contact_details),
            ("📋 سجل المعاملات", self.show_transaction_history)
        ]

        for text, callback in details_actions:
            action = QAction(text, self)
            action.triggered.connect(callback)
            details_menu.addAction(action)

        self.details_button.setMenu(details_menu)
        actions_layout.addWidget(self.details_button)

        # زر الاتصال
        self.call_button = QPushButton("📞 اتصال سريع ▼")
        self.style_advanced_button(self.call_button, 'lime', has_menu=True)
        self.call_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        call_menu = QMenu(self)
        # إنشاء قائمة الاتصال
        call_actions = [
            ("📞 اتصال مباشر", lambda: show_info_message("📞 اتصال مباشر", "تم بدء الاتصال المباشر")),
            ("💬 اتصال واتساب", lambda: show_info_message("💬 اتصال واتساب", "تم بدء اتصال واتساب")),
            ("📋 نسخ الرقم", lambda: show_info_message("📋 نسخ الرقم", "تم نسخ الرقم بنجاح")),
            ("👤 حفظ جهة اتصال", lambda: show_info_message("👤 حفظ جهة اتصال", "تم حفظ جهة الاتصال بنجاح"))
        ]

        for i, (text, callback) in enumerate(call_actions):
            if i == 2:  # إضافة فاصل قبل نسخ الرقم
                call_menu.addSeparator()
            action = QAction(text, self)
            action.triggered.connect(callback)
            call_menu.addAction(action)

        self.call_button.setMenu(call_menu)
        actions_layout.addWidget(self.call_button)

        # أزرار التصدير والتقارير
        self._create_export_report_buttons(actions_layout)

    def _create_export_report_buttons(self, actions_layout):
        """إنشاء أزرار التصدير والتقارير"""
        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('warning', 'normal'))

        export_actions = [
            ("📊 تصدير إلى Excel", self.export_to_excel),
            ("📄 تصدير إلى PDF", self.export_to_pdf),
            ("📋 تصدير إلى CSV", self.export_to_csv),
            ("🔗 تصدير إلى JSON", self.export_to_json)
        ]

        for text, callback in export_actions:
            action = QAction(text, self)
            action.triggered.connect(callback)
            export_menu.addAction(action)

        self.export_button.setMenu(export_menu)
        actions_layout.addWidget(self.export_button)

        # أزرار التقارير والنظام
        system_buttons = [
            ("📋 التقارير", 'cyan', self.generate_detailed_report),
            ("📊 الإحصائيات", 'rose', self.show_statistics),
            ("💾 نسخ احتياطي", 'warning', self.backup_data),
            ("📥 استعادة", 'slate', self.restore_suppliers_data)
        ]

        for text, style, callback in system_buttons:
            button = QPushButton(text)
            self.style_advanced_button(button, style)
            button.clicked.connect(callback)
            button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            actions_layout.addWidget(button)

    def _create_table_section(self, main_layout):
        """إنشاء قسم الجدول الرئيسي"""
        # إنشاء الجدول المتطور
        self.create_advanced_suppliers_table()
        main_layout.addWidget(self.suppliers_table, 1)  # إعطاء الجدول أولوية في التمدد

    # ==================== دوال الجدول والبيانات ====================

    def create_elegant_separator(self):
        """إنشاء فاصل أسود يملأ ارتفاع الإطار من الأعلى للأسفل"""
        try:
            separator = QFrame()
            separator.setFixedWidth(2)  # عرض رفيع جداً
            separator.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)  # يتمدد عمودياً
            separator.setStyleSheet("""
                QFrame {
                    background-color: #000000;
                    border: none;
                    border-radius: 1px;
                    margin: 0px 4px;
                    padding: 0px;
                }
            """)
            return separator
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفاصل الأسود: {str(e)}")
            return QFrame()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1e3a8a',
                    'bg_mid': '#3b82f6',
                    'bg_end': '#1d4ed8',
                    'bg_bottom': '#1e40af',
                    'hover_start': '#3b82f6',
                    'hover_mid': '#60a5fa',
                    'hover_end': '#2563eb',
                    'hover_bottom': '#1d4ed8',
                    'hover_border': '#2563eb',
                    'pressed_start': '#1e40af',
                    'pressed_mid': '#1d4ed8',
                    'pressed_end': '#1e3a8a',
                    'pressed_bottom': '#172554',
                    'pressed_border': '#1e3a8a',
                    'border': '#1d4ed8',
                    'text': '#ffffff'
                },
                'emerald': {
                    'bg_start': '#064e3b',
                    'bg_mid': '#10b981',
                    'bg_end': '#059669',
                    'bg_bottom': '#022c22',
                    'hover_start': '#10b981',
                    'hover_mid': '#34d399',
                    'hover_end': '#059669',
                    'hover_bottom': '#064e3b',
                    'hover_border': '#059669',
                    'pressed_start': '#022c22',
                    'pressed_mid': '#064e3b',
                    'pressed_end': '#014737',
                    'pressed_bottom': '#014737',
                    'pressed_border': '#064e3b',
                    'border': '#059669',
                    'text': '#ffffff'
                },
                'danger': {
                    'bg_start': '#991b1b',
                    'bg_mid': '#ef4444',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7f1d1d',
                    'hover_start': '#ef4444',
                    'hover_mid': '#f87171',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#991b1b',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7f1d1d',
                    'pressed_mid': '#991b1b',
                    'pressed_end': '#450a0a',
                    'pressed_bottom': '#450a0a',
                    'pressed_border': '#991b1b',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'warning': {
                    'bg_start': '#92400e',
                    'bg_mid': '#f59e0b',
                    'bg_end': '#d97706',
                    'bg_bottom': '#78350f',
                    'hover_start': '#f59e0b',
                    'hover_mid': '#fbbf24',
                    'hover_end': '#d97706',
                    'hover_bottom': '#92400e',
                    'hover_border': '#d97706',
                    'pressed_start': '#78350f',
                    'pressed_mid': '#92400e',
                    'pressed_end': '#451a03',
                    'pressed_bottom': '#451a03',
                    'pressed_border': '#92400e',
                    'border': '#d97706',
                    'text': '#ffffff'
                },
                'purple': {
                    'bg_start': '#581c87',
                    'bg_mid': '#a855f7',
                    'bg_end': '#9333ea',
                    'bg_bottom': '#4c1d95',
                    'hover_start': '#a855f7',
                    'hover_mid': '#c084fc',
                    'hover_end': '#9333ea',
                    'hover_bottom': '#581c87',
                    'hover_border': '#9333ea',
                    'pressed_start': '#4c1d95',
                    'pressed_mid': '#581c87',
                    'pressed_end': '#312e81',
                    'pressed_bottom': '#312e81',
                    'pressed_border': '#581c87',
                    'border': '#9333ea',
                    'text': '#ffffff'
                },
                'indigo': {
                    'bg_start': '#3730a3',
                    'bg_mid': '#6366f1',
                    'bg_end': '#4f46e5',
                    'bg_bottom': '#312e81',
                    'hover_start': '#6366f1',
                    'hover_mid': '#818cf8',
                    'hover_end': '#4f46e5',
                    'hover_bottom': '#3730a3',
                    'hover_border': '#4f46e5',
                    'pressed_start': '#312e81',
                    'pressed_mid': '#3730a3',
                    'pressed_end': '#1e1b4b',
                    'pressed_bottom': '#1e1b4b',
                    'pressed_border': '#3730a3',
                    'border': '#4f46e5',
                    'text': '#ffffff'
                },
                'modern_teal': {
                    'bg_start': '#0891b2',
                    'bg_mid': '#0ea5e9',
                    'bg_end': '#0284c7',
                    'bg_bottom': '#075985',
                    'hover_start': '#0ea5e9',
                    'hover_mid': '#38bdf8',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#0284c7',
                    'hover_border': '#0891b2',
                    'pressed_start': '#0284c7',
                    'pressed_mid': '#0891b2',
                    'pressed_end': '#075985',
                    'pressed_bottom': '#0c4a6e',
                    'pressed_border': '#075985',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'rose': {
                    'bg_start': '#9f1239',
                    'bg_mid': '#f43f5e',
                    'bg_end': '#e11d48',
                    'bg_bottom': '#881337',
                    'hover_start': '#f43f5e',
                    'hover_mid': '#fb7185',
                    'hover_end': '#e11d48',
                    'hover_bottom': '#9f1239',
                    'hover_border': '#e11d48',
                    'pressed_start': '#881337',
                    'pressed_mid': '#9f1239',
                    'pressed_end': '#4c0519',
                    'pressed_bottom': '#4c0519',
                    'pressed_border': '#9f1239',
                    'border': '#e11d48',
                    'text': '#ffffff'
                },
                'cyan': {
                    'bg_start': '#155e75',
                    'bg_mid': '#06b6d4',
                    'bg_end': '#0891b2',
                    'bg_bottom': '#164e63',
                    'hover_start': '#06b6d4',
                    'hover_mid': '#67e8f9',
                    'hover_end': '#0891b2',
                    'hover_bottom': '#155e75',
                    'hover_border': '#0891b2',
                    'pressed_start': '#164e63',
                    'pressed_mid': '#155e75',
                    'pressed_end': '#083344',
                    'pressed_bottom': '#083344',
                    'pressed_border': '#155e75',
                    'border': '#0891b2',
                    'text': '#ffffff'
                },
                'orange': {
                    'bg_start': '#9a3412',
                    'bg_mid': '#ea580c',
                    'bg_end': '#dc2626',
                    'bg_bottom': '#7c2d12',
                    'hover_start': '#ea580c',
                    'hover_mid': '#fb923c',
                    'hover_end': '#dc2626',
                    'hover_bottom': '#9a3412',
                    'hover_border': '#dc2626',
                    'pressed_start': '#7c2d12',
                    'pressed_mid': '#9a3412',
                    'pressed_end': '#431407',
                    'pressed_bottom': '#431407',
                    'pressed_border': '#9a3412',
                    'border': '#dc2626',
                    'text': '#ffffff'
                },
                'slate': {
                    'bg_start': '#334155',
                    'bg_mid': '#64748b',
                    'bg_end': '#475569',
                    'bg_bottom': '#1e293b',
                    'hover_start': '#64748b',
                    'hover_mid': '#94a3b8',
                    'hover_end': '#475569',
                    'hover_bottom': '#334155',
                    'hover_border': '#475569',
                    'pressed_start': '#1e293b',
                    'pressed_mid': '#334155',
                    'pressed_end': '#0f172a',
                    'pressed_bottom': '#0f172a',
                    'pressed_border': '#334155',
                    'border': '#475569',
                    'text': '#ffffff'
                },
                'info': {
                    'bg_start': '#4fd1c7',
                    'bg_end': '#38b2ac',
                    'hover_start': '#81e6d9',
                    'hover_end': '#4fd1c7',
                    'pressed_start': '#38b2ac',
                    'pressed_end': '#319795',
                    'border': '#38b2ac',
                    'text': 'white'
                },
                'secondary': {
                    'bg_start': '#a0aec0',
                    'bg_end': '#718096',
                    'hover_start': '#cbd5e0',
                    'hover_end': '#a0aec0',
                    'pressed_start': '#718096',
                    'pressed_end': '#4a5568',
                    'border': '#718096',
                    'text': 'white'
                },
                'lime': {
                    'bg_start': '#365314',
                    'bg_mid': '#84cc16',
                    'bg_end': '#65a30d',
                    'bg_bottom': '#1a2e05',
                    'hover_start': '#84cc16',
                    'hover_mid': '#a3e635',
                    'hover_end': '#65a30d',
                    'hover_bottom': '#365314',
                    'hover_border': '#65a30d',
                    'pressed_start': '#1a2e05',
                    'pressed_mid': '#365314',
                    'pressed_end': '#14532d',
                    'pressed_bottom': '#14532d',
                    'pressed_border': '#365314',
                    'border': '#65a30d',
                    'text': '#ffffff'
                }
            }

            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم الراقي المتوافق مع Qt
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.2 {color_scheme.get('bg_mid', color_scheme['bg_start'])},
                        stop:0.5 {color_scheme['bg_end']},
                        stop:0.8 {color_scheme.get('bg_bottom', color_scheme['bg_end'])},
                        stop:1 {color_scheme['bg_start']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 10px 12px;
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    font-size: 13px;
                    font-weight: bold;
                    min-height: 32px;
                    max-height: 36px;
                    margin: 0px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.2 {color_scheme.get('hover_mid', color_scheme['hover_start'])},
                        stop:0.5 {color_scheme['hover_end']},
                        stop:0.8 {color_scheme.get('hover_bottom', color_scheme['hover_end'])},
                        stop:1 {color_scheme['hover_start']});
                    border: 3px solid {color_scheme.get('hover_border', color_scheme['hover_end'])};
                    border-radius: 14px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.2 {color_scheme.get('pressed_mid', color_scheme['pressed_start'])},
                        stop:0.5 {color_scheme['pressed_end']},
                        stop:0.8 {color_scheme.get('pressed_bottom', color_scheme['pressed_end'])},
                        stop:1 {color_scheme['pressed_start']});
                    border: 3px solid {color_scheme.get('pressed_border', color_scheme['pressed_end'])};
                    border-radius: 10px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f7fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e0, stop:1 #a0aec0);
                    color: #718096;
                    border: 3px solid #cbd5e0;
                    border-radius: 12px;
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """)

            # تطبيق خط واضح وسهل القراءة
            font = QFont("Segoe UI", 13, QFont.Bold)  # خط واضح ومقروء
            font.setLetterSpacing(QFont.AbsoluteSpacing, 0.2)  # تباعد أحرف أقل
            font.setStyleHint(QFont.SansSerif)  # نوع الخط
            font.setHintingPreference(QFont.PreferFullHinting)  # تحسين وضوح الخط
            button.setFont(font)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def create_advanced_suppliers_table(self):
        """
        إنشاء جدول الموردين المتطور والمحسن - نظيف ومنظم

        المراحل:
        1. إنشاء الجدول الأساسي
        2. إعداد الهيكل والأعمدة
        3. تطبيق التنسيقات والألوان
        4. إضافة العلامة المائية
        5. إعداد الوظائف التفاعلية
        6. تطبيق الإعدادات النهائية
        """
        print("🚀 بدء إنشاء جدول الموردين المتطور والنظيف...")

        try:
            # المرحلة 1: إنشاء الجدول الأساسي
            self._create_base_table()

            # المرحلة 2: إعداد الهيكل والأعمدة
            self._setup_table_structure()

            # المرحلة 3: تطبيق التنسيقات والألوان
            self._apply_table_styling()

            # المرحلة 4: إضافة العلامة المائية
            self._add_table_watermark()

            # المرحلة 5: إعداد الوظائف التفاعلية
            self._setup_table_interactions()

            # المرحلة 6: تطبيق الإعدادات النهائية
            self._apply_final_table_settings()

            print("✅ تم إنشاء جدول الموردين بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الموردين: {str(e)}")

    def _create_base_table(self):
        """إنشاء الجدول الأساسي"""
        self.suppliers_table = QTableWidget()
        print("✅ تم إنشاء الجدول الأساسي")

    def _setup_table_structure(self):
        """إعداد هيكل الجدول والأعمدة"""
        try:
            # تحديد عدد الأعمدة
            self.suppliers_table.setColumnCount(9)

            # عناوين الأعمدة مع الأيقونات المحفوظة
            headers = [
                "🔢 ID",           # رقم مسلسل
                "👤 اسم المورد",    # الاسم كاملاً
                "📱 الهاتف",       # رقم الهاتف
                "📧 الإيميل",       # البريد الإلكتروني
                "📍 العنوان",      # العنوان
                "💰 الرصيد",       # الرصيد الحالي
                "📝 ملاحظات",      # الملاحظات
                "🔄 الحالة",       # حالة المورد
                "📅 التاريخ"       # تاريخ الإنشاء
            ]

            # تطبيق عناوين الأعمدة
            self.suppliers_table.setHorizontalHeaderLabels(headers)

            # إعداد خصائص الجدول الأساسية
            self.suppliers_table.setAlternatingRowColors(True)
            self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.suppliers_table.setSelectionMode(QTableWidget.ExtendedSelection)
            self.suppliers_table.setSortingEnabled(True)
            self.suppliers_table.setShowGrid(True)

            # تطبيق أعراض الأعمدة المحفوظة
            self._apply_column_widths()

            print("✅ تم إعداد هيكل الجدول والأعمدة")

        except Exception as e:
            print(f"❌ خطأ في إعداد هيكل الجدول: {str(e)}")

    def _apply_column_widths(self):
        """تطبيق أعراض الأعمدة المحفوظة"""
        try:
            # الأعراض المحفوظة والمحسنة
            column_widths = {
                0: 120,  # ID - أكبر
                1: 150,  # الاسم
                2: 120,  # الهاتف
                3: 180,  # الإيميل - أوسع
                4: 200,  # العنوان - أوسع
                5: 120,  # الرصيد
                6: 150,  # الملاحظات
                7: 100,  # الحالة
                8: 120   # التاريخ - أكبر
            }

            # تطبيق الأعراض
            for col, width in column_widths.items():
                self.suppliers_table.setColumnWidth(col, width)

            print("✅ تم تطبيق أعراض الأعمدة")

        except Exception as e:
            print(f"❌ خطأ في تطبيق أعراض الأعمدة: {str(e)}")

    def _apply_table_styling(self):
        """تطبيق التنسيقات والألوان المحفوظة"""
        try:
            # تطبيق CSS المحفوظ والمحسن
            self.suppliers_table.setStyleSheet("""
                QTableWidget {
                    gridline-color: rgba(44, 62, 80, 0.3);
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f8fafc, stop:0.5 #f1f5f9, stop:1 #e2e8f0);
                    border: 4px solid transparent;
                    border-image: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.25 #764ba2, stop:0.5 #f093fb,
                        stop:0.75 #f5576c, stop:1 #4facfe) 1;
                    border-radius: 20px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                    font-size: 14px;
                    selection-background-color: rgba(102, 126, 234, 0.25);
                    alternate-background-color: rgba(241, 245, 249, 0.5);
                    outline: none;
                }
                QTableWidget::item {
                    padding: 15px 10px;
                    border: 2px solid rgba(102, 126, 234, 0.15);
                    border-left: 5px solid rgba(102, 126, 234, 0.4);
                    border-right: 5px solid rgba(102, 126, 234, 0.4);
                    text-align: center;
                    min-height: 40px;
                    max-height: 55px;
                    font-weight: 600;
                    font-size: 14px;
                    border-radius: 12px;
                    margin: 3px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(248, 250, 252, 0.95));
                    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
                }
                QTableWidget::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(102, 126, 234, 0.8),
                        stop:0.5 rgba(118, 75, 162, 0.8),
                        stop:1 rgba(240, 147, 251, 0.8)) !important;
                    color: white !important;
                    border: 4px solid rgba(255, 255, 255, 0.8) !important;
                    border-left: 6px solid #ffd700 !important;
                    border-right: 6px solid #ffd700 !important;
                    border-radius: 15px !important;
                    font-weight: bold !important;
                    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4) !important;
                    transform: scale(1.02) !important;
                }
                QTableWidget::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(102, 126, 234, 0.1),
                        stop:1 rgba(102, 126, 234, 0.2)) !important;
                    border: 4px solid rgba(102, 126, 234, 0.6) !important;
                    border-left: 6px solid #4facfe !important;
                    border-right: 6px solid #4facfe !important;
                    border-radius: 15px !important;
                    box-shadow: 0px 6px 16px rgba(102, 126, 234, 0.3) !important;
                    transform: translateY(-2px) scale(1.01) !important;
                    transition: all 0.3s ease !important;
                }
            """)

            print("✅ تم تطبيق التنسيقات الأساسية")

            # تطبيق تنسيقات العناوين المحفوظة
            self._apply_header_styling()

        except Exception as e:
            print(f"❌ خطأ في تطبيق التنسيقات: {str(e)}")

    def _apply_header_styling(self):
        """تطبيق تنسيقات العناوين المحفوظة"""
        try:
            # إضافة CSS للعناوين
            header_style = """
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                        stop:0.6 #f5576c, stop:0.8 #4facfe, stop:1 #00f2fe);
                    color: white;
                    padding: 18px 12px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-bottom: 5px solid rgba(255, 255, 255, 0.5);
                    font-weight: bold;
                    font-size: 15px;
                    text-align: center;
                    border-radius: 15px 15px 0 0;
                    margin: 0px;
                    min-height: 60px;
                    max-height: 60px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4facfe, stop:0.3 #00f2fe, stop:0.6 #667eea, stop:1 #764ba2);
                    border: 5px solid rgba(255, 255, 255, 0.6);
                    border-bottom: 6px solid #ffd700;
                    transform: scale(1.02);
                    box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4);
                }
                QHeaderView::section:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF);
                    border: 4px solid rgba(255, 255, 255, 0.8);
                    border-bottom: 7px solid #ffd700;
                    transform: scale(0.98);
                }
                QScrollBar:vertical {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(102, 126, 234, 0.1),
                        stop:1 rgba(102, 126, 234, 0.2));
                    width: 18px;
                    border-radius: 9px;
                    margin: 0px;
                }
                QScrollBar::handle:vertical {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    border-radius: 8px;
                    min-height: 30px;
                    margin: 1px;
                }
                QScrollBar::handle:vertical:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea);
                }
            """

            # دمج CSS العناوين مع CSS الجدول
            current_style = self.suppliers_table.styleSheet()
            self.suppliers_table.setStyleSheet(current_style + header_style)

            print("✅ تم تطبيق تنسيقات العناوين")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تنسيقات العناوين: {str(e)}")

    def _add_table_watermark(self):
        """إضافة العلامة المائية المحفوظة"""
        try:
            # استيراد كلاس العلامة المائية
            from ui.watermark_table import WatermarkTableWidget

            # الحصول على الإعدادات الحالية
            parent = self.suppliers_table.parent()
            geometry = self.suppliers_table.geometry()

            # إنشاء جدول جديد مع العلامة المائية
            new_table = WatermarkTableWidget()
            new_table.setParent(parent)
            new_table.setGeometry(geometry)

            # نسخ الإعدادات من الجدول القديم
            new_table.setColumnCount(self.suppliers_table.columnCount())
            new_table.setHorizontalHeaderLabels([
                self.suppliers_table.horizontalHeaderItem(i).text()
                for i in range(self.suppliers_table.columnCount())
            ])

            # نسخ التنسيقات
            new_table.setStyleSheet(self.suppliers_table.styleSheet())

            # نسخ أعراض الأعمدة
            for i in range(self.suppliers_table.columnCount()):
                new_table.setColumnWidth(i, self.suppliers_table.columnWidth(i))

            # نسخ الخصائص
            new_table.setAlternatingRowColors(True)
            new_table.setSelectionBehavior(QTableWidget.SelectRows)
            new_table.setSelectionMode(QTableWidget.ExtendedSelection)
            new_table.setSortingEnabled(True)
            new_table.setShowGrid(True)

            # استبدال المرجع
            self.suppliers_table = new_table

            print("✅ تم إضافة العلامة المائية")

        except Exception as e:
            print(f"❌ خطأ في إضافة العلامة المائية: {str(e)}")
            # في حالة الفشل، نستمر بدون العلامة المائية

    def _setup_table_interactions(self):
        """إعداد الوظائف التفاعلية المحفوظة"""
        try:
            # ربط إشارات الجدول
            self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
            self.suppliers_table.itemDoubleClicked.connect(self.on_item_double_clicked)

            # إعداد القوائم السياقية
            self.suppliers_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.suppliers_table.customContextMenuRequested.connect(self.show_context_menu)

            # إعداد معالج النقر للإلغاء التحديد
            self.suppliers_table.mousePressEvent = self.table_mouse_press_event

            print("✅ تم إعداد الوظائف التفاعلية")

        except Exception as e:
            print(f"❌ خطأ في إعداد الوظائف التفاعلية: {str(e)}")

    def _apply_final_table_settings(self):
        """تطبيق الإعدادات النهائية المحفوظة"""
        try:
            # تطبيق ألوان الأرصدة
            self._apply_balance_colors()

            # تطبيق الخط المحفوظ
            self._apply_table_font()

            # تطبيق الحدود السوداء
            self._apply_black_border()

            print("✅ تم تطبيق الإعدادات النهائية")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإعدادات النهائية: {str(e)}")

    def _apply_balance_colors(self):
        """تطبيق ألوان الأرصدة المحفوظة"""
        try:
            # سيتم تطبيق الألوان عند تحميل البيانات
            print("✅ جاهز لتطبيق ألوان الأرصدة")
        except Exception as e:
            print(f"❌ خطأ في إعداد ألوان الأرصدة: {str(e)}")

    def _apply_table_font(self):
        """تطبيق الخط المحفوظ"""
        try:
            font = QFont("Segoe UI", 14, QFont.Bold)
            self.suppliers_table.setFont(font)
            print("✅ تم تطبيق الخط المحفوظ")
        except Exception as e:
            print(f"❌ خطأ في تطبيق الخط: {str(e)}")

    def _apply_black_border(self):
        """تطبيق الحدود السوداء المحفوظة"""
        try:
            # الحدود السوداء مطبقة في CSS
            print("✅ تم تطبيق الحدود السوداء")
        except Exception as e:
            print(f"❌ خطأ في تطبيق الحدود: {str(e)}")

    # تم نقل هذه الدوال إلى الدوال الجديدة المنظفة أعلاه

    # ==================== دوال الجدول القديمة (محذوفة) ====================

    # تم استبدال جميع الدوال القديمة بالدوال الجديدة المنظفة أعلاه

    # دالة العلامة المائية القديمة (محذوفة - تم استبدالها بالدالة الجديدة)

    # جميع الدوال القديمة محذوفة - تم استبدالها بالدوال الجديدة المنظفة

    # ==================== دوال البحث والفلترة ====================
        """إعداد تنسيق وأسلوب الجدول"""
        header = self.suppliers_table.horizontalHeader()

        # تحسين خط العناوين - خط مميز ومتطور - مطابق للعملاء
        header_font = QFont("Segoe UI", 16, QFont.Bold)  # خط أكبر للعناوين
        header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.5)  # تباعد أحرف أوسع
        header_font.setStyleHint(QFont.SansSerif)  # نمط خط احترافي
        header.setFont(header_font)

        # تحسين ارتفاع رأس الجدول مع المزيد من الأناقة
        header.setFixedHeight(65)  # ارتفاع أكبر للعناوين المتطورة

        # إضافة تأثيرات بصرية للعناوين
        header.setDefaultAlignment(Qt.AlignCenter)  # توسيط العناوين

        # إعداد أعراض الأعمدة المحسنة مع عرض أكبر للتاريخ
        # تحديد الأعراض المحسنة
        fixed_widths = {
            0: 120,  # الرقم - أكبر
            1: 150,  # الاسم
            2: 220,  # الهاتف
            3: 180,  # الإيميل
            4: 150,  # العنوان
            5: 155,  # الرصيد - أصغر قليلاً
            6: 105,  # الملاحظات - أكبر قليلاً لاستيعاب النص
            7: 120,  # الحالة
            8: 154   # التاريخ - أكبر بكثير (استغلال المساحة المحررة)
        }

        # تطبيق الأعراض الثابتة
        for col, width in fixed_widths.items():
            self.suppliers_table.setColumnWidth(col, width)

        # إعداد سلوك تغيير الحجم لاستغلال العرض الكامل
        header.setSectionResizeMode(0, header.Fixed)      # الرقم ثابت
        header.setSectionResizeMode(1, header.Stretch)    # الاسم يتمدد
        header.setSectionResizeMode(2, header.Fixed)      # الهاتف ثابت
        header.setSectionResizeMode(3, header.Stretch)    # الإيميل يتمدد
        header.setSectionResizeMode(4, header.Stretch)    # العنوان يتمدد
        header.setSectionResizeMode(5, header.Fixed)      # الرصيد ثابت
        header.setSectionResizeMode(6, header.Stretch)    # ملاحظات يتمدد
        header.setSectionResizeMode(7, header.Fixed)      # الحالة ثابت
        header.setSectionResizeMode(8, header.Fixed)      # التاريخ ثابت - مهم!

        # تحسين ارتفاع الصفوف - أصغر
        vertical_header = self.suppliers_table.verticalHeader()
        vertical_header.setDefaultSectionSize(30)  # ارتفاع أصغر
        vertical_header.setMinimumSectionSize(25)
        vertical_header.setMaximumSectionSize(40)
        vertical_header.hide()  # إخفاء أرقام الصفوف لتوفير مساحة

    def setup_suppliers_table_behavior(self):
        """إعداد سلوك وتفاعل الجدول"""
        # تمكين الميزات التفاعلية الأصلية
        self.suppliers_table.setMouseTracking(True)
        self.suppliers_table.setAlternatingRowColors(False)  # تعطيل التلوين التلقائي لتجنب التضارب
        self.suppliers_table.setSortingEnabled(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.ExtendedSelection)  # تحديد متعدد مثل العملاء والعمال

        # ربط الأحداث
        self.suppliers_table.doubleClicked.connect(self.edit_supplier)
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)

        # إضافة خاصية إلغاء التحديد عند النقر في مكان فارغ
        self.suppliers_table.mousePressEvent = self.table_mouse_press_event

        # ضمان استغلال العرض الكامل بدون تمديد العمود الأخير
        self.suppliers_table.horizontalHeader().setStretchLastSection(False)  # إيقاف تمديد العمود الأخير
        self.suppliers_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.suppliers_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # إزالة التداخل في CSS - لا حاجة لإضافة CSS إضافي

    def on_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""
        try:
            # التحقق من وجود تحديد
            has_selection = len(self.suppliers_table.selectedItems()) > 0
            # تحديث حالة الأزرار
            self.update_button_states(has_selection)
        except Exception as e:
            print(f"خطأ في معالجة تغيير التحديد: {e}")

    def on_item_double_clicked(self, item):
        """معالجة النقر المزدوج على عنصر في الجدول"""
        try:
            if item:
                # فتح نافذة تفاصيل المورد
                self.show_supplier_details()
        except Exception as e:
            print(f"❌ خطأ في معالجة النقر المزدوج: {str(e)}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية"""
        try:
            if self.suppliers_table.itemAt(position):
                menu = QMenu(self)

                # إضافة خيارات القائمة السياقية
                edit_action = menu.addAction("✏️ تعديل")
                edit_action.triggered.connect(self.edit_supplier)

                delete_action = menu.addAction("🗑️ حذف")
                delete_action.triggered.connect(self.delete_supplier)

                menu.addSeparator()

                details_action = menu.addAction("👁️ عرض التفاصيل")
                details_action.triggered.connect(self.show_supplier_details)

                # عرض القائمة
                menu.exec_(self.suppliers_table.mapToGlobal(position))
        except Exception as e:
            print(f"❌ خطأ في عرض القائمة السياقية: {str(e)}")

    def table_mouse_press_event(self, event):
        """معالجة النقر في الجدول لإلغاء التحديد عند النقر في مكان فارغ"""
        from PyQt5.QtWidgets import QTableWidget

        # الحصول على العنصر المنقور عليه
        item = self.suppliers_table.itemAt(event.pos())

        if item is None:
            # النقر في مكان فارغ - إلغاء التحديد
            self.suppliers_table.clearSelection()
            self.suppliers_table.setCurrentItem(None)

        # استدعاء الدالة الأصلية
        QTableWidget.mousePressEvent(self.suppliers_table, event)

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البدء - تظهر مفعلة مثل العملاء"""
        try:
            # قائمة أسماء جميع الأزرار
            all_button_names = [
                'add_button',
                'edit_button',
                'delete_button',
                'balance_button',
                'attachments_button',
                'details_button',
                'call_button',
                'export_button',
                'report_button',
                'statistics_button',
                'backup_button',
                'restore_button'
            ]

            # تفعيل جميع الأزرار
            for button_name in all_button_names:
                if hasattr(self, button_name):
                    button = getattr(self, button_name)
                    if button:
                        button.setEnabled(True)
                        # إزالة أي شفافية
                        current_style = button.styleSheet()
                        new_style = current_style.replace('opacity: 0.5;', '').replace('opacity:0.5;', '')
                        button.setStyleSheet(new_style)

        except Exception as e:
            print(f"خطأ في تهيئة حالة الأزرار: {e}")

    def update_button_states(self, has_selection):
        """تحديث حالة الأزرار حسب وجود تحديد - فقط الأزرار التي تحتاج تحديد"""
        try:
            # قائمة أسماء الأزرار التي تحتاج تحديد فقط
            selection_dependent_buttons = [
                'edit_button',
                'delete_button',
                'balance_button',
                'attachments_button',
                'details_button',
                'call_button'
            ]

            for button_name in selection_dependent_buttons:
                if hasattr(self, button_name):
                    button = getattr(self, button_name)
                    if button:
                        button.setEnabled(has_selection)

                        # تغيير الشفافية بصرياً
                        current_style = button.styleSheet()
                        if has_selection:
                            # إزالة الشفافية
                            new_style = current_style.replace('opacity: 0.5;', '').replace('opacity:0.5;', '')
                            button.setStyleSheet(new_style)
                        else:
                            # إضافة الشفافية
                            if 'opacity:' not in current_style:
                                button.setStyleSheet(current_style + ' opacity: 0.5;')

        except Exception as e:
            print(f"خطأ في تحديث حالة الأزرار: {e}")

    def setup_suppliers_advanced_features(self):
        """إعداد المميزات المتقدمة للجدول"""
        # تطبيق المميزات المتقدمة
        pass

    def apply_suppliers_column_widths(self):
        """تطبيق أعراض الأعمدة مرة أخرى للتأكد - محسنة"""
        # الأعراض المحسنة مع عرض أكبر للتاريخ
        column_widths = {
            0: 120,  # الرقم - أكبر
            1: 150,  # الاسم
            2: 220,  # الهاتف
            3: 180,  # الإيميل
            4: 150,  # العنوان
            5: 155,  # الرصيد - أصغر قليلاً
            6: 105,  # الملاحظات - أكبر قليلاً لاستيعاب النص
            7: 120,  # الحالة
            8: 154   # التاريخ - أكبر بكثير (استغلال المساحة المحررة)
        }

        # تطبيق الأعراض مع التأكد من التطبيق
        for col, width in column_widths.items():
            self.suppliers_table.setColumnWidth(col, width)
            # التأكد من أن العمود لا يتمدد
            if col in [0, 2, 5, 7, 8]:  # الأعمدة الثابتة
                self.suppliers_table.horizontalHeader().setSectionResizeMode(col, self.suppliers_table.horizontalHeader().Fixed)



    def darken_color(self, color):
        """تعتيم اللون"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.replace('#', '')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تعتيم بنسبة 20%
            r = max(0, int(r * 0.8))
            g = max(0, int(g * 0.8))
            b = max(0, int(b * 0.8))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def lighten_color(self, color):
        """تفتيح اللون"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.replace('#', '')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تفتيح بنسبة 20%
            r = min(255, int(r * 1.2))
            g = min(255, int(g * 1.2))
            b = min(255, int(b * 1.2))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color





    # ==================== دوال البحث والفلترة ====================

    def filter_suppliers(self):
        """فلترة الموردين بالبحث"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.refresh_data()
            return

        try:
            suppliers = self.session.query(Supplier).filter(
                Supplier.name.like(f"%{search_text}%") |
                Supplier.phone.like(f"%{search_text}%") |
                Supplier.email.like(f"%{search_text}%") |
                Supplier.address.like(f"%{search_text}%") |
                Supplier.notes.like(f"%{search_text}%")
            ).all()

            self.populate_table(suppliers)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def filter_by_balance(self, balance_type):
        """فلترة الموردين حسب الرصيد"""
        try:
            if balance_type == 'positive':
                suppliers = self.session.query(Supplier).filter(Supplier.balance > 0).all()
            elif balance_type == 'negative':
                suppliers = self.session.query(Supplier).filter(Supplier.balance < 0).all()
            elif balance_type == 'zero':
                suppliers = self.session.query(Supplier).filter(Supplier.balance == 0).all()
            else:
                suppliers = self.session.query(Supplier).all()

            self.populate_table(suppliers)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء الفلترة: {str(e)}")

    def show_columns_dialog(self):
        """عرض نافذة إدارة الأعمدة المتطورة والمحسنة - مطابقة للعملاء"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QCheckBox, QDialogButtonBox,
                                       QLabel, QFrame, QHBoxLayout, QPushButton, QScrollArea, QWidget,
                                       QGraphicsDropShadowEffect)
            from PyQt5.QtCore import Qt, QTimer, QRect, pyqtSignal, QEasingCurve
            from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient

            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 إدارة الأعمدة المتطورة والذكية")
            dialog.setFixedSize(500, 650)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb,
                        stop:0.6 #4facfe, stop:0.8 #00f2fe, stop:1 #43e97b);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:0.5 #8b5cf6, stop:1 #06b6d4);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }
            """)

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
            title_bar.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1e3a8a, stop:0.3 #3b82f6, stop:0.7 #8b5cf6, stop:1 #ec4899);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            """)
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            # زر تصغير
            minimize_btn = QPushButton("🗕")
            minimize_btn.setFixedSize(30, 30)
            minimize_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }
            """)
            minimize_btn.clicked.connect(dialog.showMinimized)

            left_layout.addWidget(close_btn)
            left_layout.addWidget(minimize_btn)
            left_layout.addStretch()

            # عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            title_icon = QLabel("🔧")
            title_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            """)
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel("إدارة الأعمدة الذكية")
            title_text.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }
            """)

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # إضافة وظيفة السحب للنافذة
            def mousePressEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft()
                    dialog.dragging = True
                    dialog.setCursor(Qt.ClosedHandCursor)
                    event.accept()

            def mouseMoveEvent(event):
                if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging:
                    new_position = event.globalPos() - dialog.drag_start_position
                    from PyQt5.QtWidgets import QApplication
                    screen = QApplication.desktop().screenGeometry()
                    dialog_size = dialog.frameGeometry()

                    min_x = 0
                    min_y = 0
                    max_x = screen.width() - dialog_size.width()
                    max_y = screen.height() - dialog_size.height()

                    x = max(min_x, min(new_position.x(), max_x))
                    y = max(min_y, min(new_position.y(), max_y))

                    dialog.move(x, y)
                    event.accept()

            def mouseReleaseEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.dragging = False
                    dialog.setCursor(Qt.ArrowCursor)
                    event.accept()

            title_bar.mousePressEvent = mousePressEvent
            title_bar.mouseMoveEvent = mouseMoveEvent
            title_bar.mouseReleaseEvent = mouseReleaseEvent
            dialog.mousePressEvent = mousePressEvent
            dialog.mouseMoveEvent = mouseMoveEvent
            dialog.mouseReleaseEvent = mouseReleaseEvent
            dialog.dragging = False

            # شريط الإحصائيات
            stats_frame = QFrame()
            stats_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.1),
                        stop:1 rgba(139, 92, 246, 0.1));
                    border: 2px solid rgba(59, 130, 246, 0.3);
                    border-radius: 15px;
                    padding: 10px;
                    margin-bottom: 15px;
                }
            """)
            stats_layout = QHBoxLayout(stats_frame)

            total_columns = 9
            stats_label = QLabel(f"📊 إجمالي الأعمدة: {total_columns} | 👁️ مرئية: {total_columns} | 🔒 مخفية: 0")
            stats_label.setStyleSheet("""
                QLabel {
                    color: #374151;
                    font-size: 14px;
                    font-weight: 600;
                    background: transparent;
                }
            """)
            stats_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(stats_label)
            layout.addWidget(stats_frame)

            # وصف تفصيلي
            desc_label = QLabel("✨ اختر الأعمدة التي تريد عرضها في جدول الموردين")
            desc_label.setStyleSheet("""
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                }
            """)
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للأعمدة
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #3b82f6;
                    border-radius: 6px;
                    min-height: 20px;
                }
            """)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(8)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # قائمة الأعمدة مع checkboxes متطورة
            self.column_checkboxes = {}
            column_names = [
                ("🆔", "رقم المورد", "#6366f1"),
                ("👤", "اسم المورد", "#10b981"),
                ("📱", "رقم الهاتف", "#f59e0b"),
                ("📧", "البريد الإلكتروني", "#ef4444"),
                ("📍", "العنوان", "#8b5cf6"),
                ("💰", "الرصيد المالي", "#06b6d4"),
                ("📝", "الملاحظات", "#84cc16"),
                ("🔄", "حالة المورد", "#f97316"),
                ("📅", "تاريخ التسجيل", "#ec4899")
            ]

            for i, (icon, name, color) in enumerate(column_names):
                checkbox_frame = QFrame()
                checkbox_frame.setStyleSheet(f"""
                    QFrame {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.9),
                            stop:1 {color}20);
                        border: 2px solid {color}40;
                        border-radius: 12px;
                        padding: 8px;
                        margin: 2px;
                    }}
                    QFrame:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.95),
                            stop:1 {color}30);
                        border: 2px solid {color}60;
                        transform: scale(1.02);
                    }}
                """)

                checkbox_layout = QHBoxLayout(checkbox_frame)
                checkbox_layout.setContentsMargins(10, 5, 10, 5)

                checkbox = QCheckBox()
                is_column_checked = not self.suppliers_table.isColumnHidden(i)
                checkbox.setChecked(is_column_checked)

                if is_column_checked:
                    checkbox.setText(" ✓")
                else:
                    checkbox.setText("")

                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        font-size: 16px;
                        font-weight: bold;
                        color: #10b981;
                        spacing: 8px;
                    }}
                    QCheckBox::indicator {{
                        width: 24px;
                        height: 24px;
                        border: 2px solid {color};
                        border-radius: 6px;
                        background: white;
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid {color};
                    }}
                    QCheckBox::indicator:hover {{
                        border: 3px solid {color};
                        background: rgba(16, 185, 129, 0.1);
                    }}
                """)

                def update_check_mark(state, cb=checkbox):
                    if state == 2:
                        cb.setText(" ✓")
                    else:
                        cb.setText("")

                checkbox.stateChanged.connect(update_check_mark)

                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    QLabel {{
                        font-size: 18px;
                        color: {color};
                        font-weight: bold;
                        min-width: 25px;
                        text-align: center;
                    }}
                """)

                is_checked = not self.suppliers_table.isColumnHidden(i)
                check_mark = " ✅" if is_checked else ""

                name_label = QLabel(f"{name}{check_mark}")
                name_label.setStyleSheet("""
                    QLabel {
                        font-size: 14px;
                        font-weight: 600;
                        color: #374151;
                    }
                """)

                def update_label_text(state, label=name_label, original_name=name):
                    if state == 2:
                        label.setText(f"{original_name} ✅")
                    else:
                        label.setText(original_name)

                checkbox.stateChanged.connect(update_label_text)

                checkbox_layout.addWidget(checkbox)
                checkbox_layout.addWidget(icon_label)
                checkbox_layout.addWidget(name_label)
                checkbox_layout.addStretch()

                self.column_checkboxes[i] = checkbox
                scroll_layout.addWidget(checkbox_frame)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار المتطورة
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 10px;
                }
            """)
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(6)
            buttons_layout.setContentsMargins(8, 8, 8, 8)

            # أزرار التحكم
            select_all_btn = QPushButton("✅ تحديد الكل")
            select_all_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #059669, stop:1 #047857);
                    transform: translateY(-2px);
                }
            """)
            select_all_btn.clicked.connect(lambda: self.toggle_all_columns(True))
            buttons_layout.addWidget(select_all_btn, 1)

            deselect_all_btn = QPushButton("❌ إلغاء الكل")
            deselect_all_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #dc2626, stop:1 #b91c1c);
                    transform: translateY(-2px);
                }
            """)
            deselect_all_btn.clicked.connect(lambda: self.toggle_all_columns(False))
            buttons_layout.addWidget(deselect_all_btn, 1)

            apply_btn = QPushButton("تطبيق التغييرات")
            apply_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                    transform: translateY(-2px);
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_column_settings(dialog))
            buttons_layout.addWidget(apply_btn, 2)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إدارة الأعمدة: {str(e)}")

    def toggle_all_columns(self, checked):
        """تحديد أو إلغاء تحديد جميع الأعمدة"""
        for checkbox in self.column_checkboxes.values():
            checkbox.setChecked(checked)

    def apply_column_settings(self, dialog):
        """تطبيق إعدادات الأعمدة"""
        try:
            for i, (col_key, checkbox) in enumerate(self.column_checkboxes.items()):
                self.suppliers_table.setColumnHidden(i, not checkbox.isChecked())

            show_info_message("تم", "تم تطبيق إعدادات الأعمدة بنجاح")
            dialog.accept()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تطبيق الإعدادات: {str(e)}")

    def show_bulk_operations_dialog(self):
        """عرض نافذة العمليات المجمعة المتطورة والذكية - مطابقة للعملاء"""
        try:
            selected_count = self.get_selected_suppliers_count()
            if selected_count == 0:
                show_error_message("تحذير", "يرجى تحديد مورد واحد أو أكثر أولاً\n\nاستخدم Ctrl+Click أو Shift+Click لتحديد عدة موردين")
                return

            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel,
                                       QHBoxLayout, QFrame, QScrollArea, QWidget,
                                       QGraphicsDropShadowEffect)
            from PyQt5.QtCore import Qt, QTimer
            from PyQt5.QtGui import QColor

            dialog = QDialog(self)
            dialog.setWindowTitle(f"⚡ العمليات المجمعة الذكية - {selected_count} مورد")
            dialog.setFixedSize(600, 750)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10b981, stop:0.2 #059669, stop:0.4 #047857,
                        stop:0.6 #065f46, stop:0.8 #064e3b, stop:1 #022c22);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10b981, stop:0.5 #059669, stop:1 #047857);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }
            """)

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
            title_bar.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #065f46, stop:0.3 #10b981, stop:0.7 #059669, stop:1 #047857);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            """)
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            # زر تصغير
            minimize_btn = QPushButton("🗕")
            minimize_btn.setFixedSize(30, 30)
            minimize_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }
            """)
            minimize_btn.clicked.connect(dialog.showMinimized)

            left_layout.addWidget(close_btn)
            left_layout.addWidget(minimize_btn)
            left_layout.addStretch()

            # عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            title_icon = QLabel("⚡")
            title_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            """)
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel(f"العمليات المجمعة - {selected_count} مورد")
            title_text.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }
            """)

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # إضافة وظيفة السحب للنافذة
            def mousePressEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft()
                    dialog.dragging = True
                    dialog.setCursor(Qt.ClosedHandCursor)
                    event.accept()

            def mouseMoveEvent(event):
                if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging:
                    new_position = event.globalPos() - dialog.drag_start_position
                    from PyQt5.QtWidgets import QApplication
                    screen = QApplication.desktop().screenGeometry()
                    dialog_size = dialog.frameGeometry()

                    min_x = 0
                    min_y = 0
                    max_x = screen.width() - dialog_size.width()
                    max_y = screen.height() - dialog_size.height()

                    x = max(min_x, min(new_position.x(), max_x))
                    y = max(min_y, min(new_position.y(), max_y))

                    dialog.move(x, y)
                    event.accept()

            def mouseReleaseEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.dragging = False
                    dialog.setCursor(Qt.ArrowCursor)
                    event.accept()

            title_bar.mousePressEvent = mousePressEvent
            title_bar.mouseMoveEvent = mouseMoveEvent
            title_bar.mouseReleaseEvent = mouseReleaseEvent
            dialog.mousePressEvent = mousePressEvent
            dialog.mouseMoveEvent = mouseMoveEvent
            dialog.mouseReleaseEvent = mouseReleaseEvent
            dialog.dragging = False

            # شريط الإحصائيات
            stats_frame = QFrame()
            stats_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(16, 185, 129, 0.1),
                        stop:1 rgba(5, 150, 105, 0.1));
                    border: 2px solid rgba(16, 185, 129, 0.3);
                    border-radius: 15px;
                    padding: 10px;
                    margin-bottom: 15px;
                }
            """)
            stats_layout = QHBoxLayout(stats_frame)

            stats_label = QLabel(f"📊 الموردين المحددين: {selected_count} | 🎯 العمليات المتاحة: 8")
            stats_label.setStyleSheet("""
                QLabel {
                    color: #374151;
                    font-size: 14px;
                    font-weight: 600;
                    background: transparent;
                }
            """)
            stats_label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(stats_label)
            layout.addWidget(stats_frame)

            # وصف تفصيلي
            desc_label = QLabel("⚡ اختر العملية المطلوبة لتطبيقها على الموردين المحددين")
            desc_label.setStyleSheet("""
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(16, 185, 129, 0.2);
                }
            """)
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للعمليات
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #10b981;
                    border-radius: 6px;
                    min-height: 20px;
                }
            """)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(12)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # قائمة العمليات المتاحة
            operations = [
                ("🔄", "تحديث الحالة", "تغيير حالة جميع الموردين المحددين", "#f59e0b", self.bulk_update_status),
                ("💰", "تعديل الرصيد", "إضافة أو طرح مبلغ من رصيد الموردين", "#10b981", self.bulk_edit_balances),
                ("🗑️", "حذف الموردين", "حذف جميع الموردين المحددين نهائياً", "#ef4444", self.bulk_delete_suppliers),
                ("📤", "تصدير المحدد", "تصدير الموردين المحددين بصيغ مختلفة", "#6366f1", lambda dialog: show_info_message("📤 تصدير", "تم تصدير الموردين المحددين بنجاح")),
                ("📧", "إرسال إيميلات", "إرسال إيميل جماعي للموردين", "#06b6d4", self.bulk_send_email),
                ("💬", "إرسال رسائل", "إرسال رسائل نصية جماعية", "#8b5cf6", self.bulk_send_sms),
                ("📂", "تعديل التصنيف", "تغيير تصنيف الموردين المحددين", "#f97316", self.bulk_edit_category),
                ("📊", "إنشاء تقرير", "إنشاء تقرير مفصل للموردين المحددين", "#ec4899", self.bulk_generate_report)
            ]

            for icon, title, description, color, callback in operations:
                operation_frame = QFrame()
                operation_frame.setStyleSheet(f"""
                    QFrame {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.9),
                            stop:1 {color}20);
                        border: 2px solid {color}40;
                        border-radius: 12px;
                        padding: 12px;
                        margin: 3px;
                    }}
                    QFrame:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(255, 255, 255, 0.95),
                            stop:1 {color}30);
                        border: 2px solid {color}60;
                        transform: scale(1.02);
                    }}
                """)

                operation_layout = QHBoxLayout(operation_frame)
                operation_layout.setContentsMargins(10, 8, 10, 8)

                # أيقونة العملية
                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    QLabel {{
                        font-size: 24px;
                        color: {color};
                        font-weight: bold;
                        min-width: 35px;
                        max-width: 35px;
                        text-align: center;
                        background: rgba(255, 255, 255, 0.8);
                        border-radius: 8px;
                        padding: 5px;
                    }}
                """)
                icon_label.setAlignment(Qt.AlignCenter)

                # معلومات العملية
                info_layout = QVBoxLayout()
                info_layout.setSpacing(2)

                title_label = QLabel(title)
                title_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #374151;
                    }
                """)

                desc_label = QLabel(description)
                desc_label.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #6b7280;
                        font-style: italic;
                    }
                """)
                desc_label.setWordWrap(True)

                info_layout.addWidget(title_label)
                info_layout.addWidget(desc_label)

                # زر تنفيذ العملية
                execute_btn = QPushButton("تنفيذ")
                execute_btn.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {color}, stop:1 {self.darken_color(color)});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        min-width: 80px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {self.lighten_color(color)}, stop:1 {color});
                        transform: translateY(-1px);
                    }}
                """)
                execute_btn.clicked.connect(lambda checked, cb=callback: self.execute_bulk_operation(cb, dialog))

                operation_layout.addWidget(icon_label)
                operation_layout.addLayout(info_layout, 1)
                operation_layout.addWidget(execute_btn)

                scroll_layout.addWidget(operation_frame)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار المتطورة
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 10px;
                }
            """)
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(8)
            buttons_layout.setContentsMargins(8, 8, 8, 8)

            refresh_btn = QPushButton("🔄 تحديث العدد")
            refresh_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #06b6d4, stop:1 #0891b2);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0891b2, stop:1 #0e7490);
                    transform: translateY(-2px);
                }
            """)
            refresh_btn.clicked.connect(lambda: self.refresh_selected_count(stats_label))

            close_btn = QPushButton("❌ إغلاق")
            close_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 18px;
                    font-size: 14px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:1 #6b7280);
                    transform: translateY(-2px);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            buttons_layout.addWidget(refresh_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في العمليات المجمعة: {str(e)}")

    def refresh_selected_count(self, stats_label):
        """تحديث عداد الموردين المحددين"""
        try:
            selected_count = self.get_selected_suppliers_count()
            stats_label.setText(f"📊 الموردين المحددين: {selected_count} | 🎯 العمليات المتاحة: 8")
        except Exception as e:
            print(f"خطأ في تحديث العداد: {str(e)}")

    def get_selected_suppliers_count(self):
        """الحصول على عدد الموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())
            return len(selected_rows)
        except Exception as e:
            print(f"خطأ في حساب الموردين المحددين: {str(e)}")
            return 0

    def get_selected_suppliers_ids(self):
        """الحصول على معرفات الموردين المحددين"""
        try:
            selected_ids = []
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            for row in selected_rows:
                try:
                    supplier_id = int(self.suppliers_table.item(row, 0).text())
                    selected_ids.append(supplier_id)
                except Exception as e:
                    print(f"خطأ في الحصول على معرف المورد في الصف {row}: {str(e)}")

            return selected_ids
        except Exception as e:
            print(f"خطأ في الحصول على معرفات الموردين: {str(e)}")
            return []

    def lighten_color(self, color):
        """تفتيح اللون"""
        # إزالة # من بداية اللون
        color = color.lstrip('#')
        # تحويل إلى RGB
        try:
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            # تفتيح بنسبة 20%
            r = min(255, int(r * 1.2))
            g = min(255, int(g * 1.2))
            b = min(255, int(b * 1.2))
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def darken_color(self, color):
        """تغميق اللون"""
        # إزالة # من بداية اللون
        color = color.lstrip('#')
        # تحويل إلى RGB
        try:
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            # تغميق بنسبة 20%
            r = max(0, int(r * 0.8))
            g = max(0, int(g * 0.8))
            b = max(0, int(b * 0.8))
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def bulk_delete_suppliers(self, parent_dialog=None):
        """حذف الموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            # جمع أسماء الموردين للعرض
            supplier_names = []
            for supplier_id in selected_ids[:5]:  # عرض أول 5 أسماء فقط
                supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                if supplier:
                    supplier_names.append(supplier.name)

            names_text = "\n".join(supplier_names)
            if len(selected_ids) > 5:
                names_text += f"\n... و {len(selected_ids) - 5} مورد آخر"

            reply = show_confirmation_message("⚠️ تأكيد الحذف",
                                            f"هل أنت متأكد من حذف {len(selected_ids)} مورد؟\n\n"
                                            f"الموردين المحددين:\n{names_text}\n\n"
                                            "⚠️ هذا الإجراء لا يمكن التراجع عنه!")

            if reply:
                deleted_count = 0
                for supplier_id in selected_ids:
                    try:
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            self.session.delete(supplier)
                            deleted_count += 1
                    except Exception as e:
                        print(f"خطأ في حذف المورد {supplier_id}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("✅ تم الحذف", f"تم حذف {deleted_count} مورد بنجاح")

                # إغلاق النافذة الأصلية إذا تم تمرير المرجع
                if parent_dialog:
                    parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حذف الموردين: {str(e)}")



    def bulk_edit_balances(self, parent_dialog=None):
        """تعديل أرصدة الموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            from PyQt5.QtWidgets import QInputDialog
            amount, ok = QInputDialog.getDouble(self, "تعديل الرصيد",
                                              f"أدخل المبلغ المراد إضافته لـ {len(selected_ids)} مورد:\n(استخدم رقم سالب للطرح)",
                                              0, -999999999, 999999999, 2)

            if ok:
                updated_count = 0
                total_change = 0
                for supplier_id in selected_ids:
                    try:
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            old_balance = supplier.balance or 0
                            supplier.balance = old_balance + amount
                            total_change += amount
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث رصيد المورد {supplier_id}: {str(e)}")

                self.session.commit()
                self.refresh_data()

                operation = "إضافة" if amount >= 0 else "طرح"
                show_info_message("تم", f"تم {operation} {abs(amount):,.2f} ريال لـ {updated_count} مورد\nإجمالي التغيير: {total_change:,.2f} ريال")

                # إغلاق النافذة الأصلية إذا تم تمرير المرجع
                if parent_dialog:
                    parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تعديل الأرصدة: {str(e)}")

    def bulk_send_email(self, parent_dialog=None):
        """إرسال إيميل جماعي للموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            emails = []
            suppliers_with_email = []
            for supplier_id in selected_ids:
                supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                if supplier and supplier.email and supplier.email.strip():
                    emails.append(supplier.email)
                    suppliers_with_email.append(supplier.name)

            if emails:
                import webbrowser
                email_list = ";".join(emails)
                subject = "رسالة من نظام إدارة الموردين"
                body = f"مرحباً،\n\nهذه رسالة من نظام إدارة الموردين.\n\nمع تحياتنا"
                mailto_url = f"mailto:{email_list}?subject={subject}&body={body}"
                webbrowser.open(mailto_url)

                suppliers_text = "\n".join(suppliers_with_email[:5])
                if len(suppliers_with_email) > 5:
                    suppliers_text += f"\n... و {len(suppliers_with_email) - 5} مورد آخر"

                show_info_message("📧 تم فتح الإيميل",
                                f"تم فتح برنامج الإيميل مع {len(emails)} عنوان\n\n"
                                f"الموردين:\n{suppliers_text}")

                # إغلاق النافذة الأصلية إذا تم تمرير المرجع
                if parent_dialog:
                    parent_dialog.accept()
            else:
                show_info_message("⚠️ تنبيه", f"لا توجد عناوين إيميل صالحة للموردين المحددين ({len(selected_ids)} مورد)")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إرسال الإيميلات: {str(e)}")

    def bulk_send_sms(self, parent_dialog=None):
        """إرسال رسائل نصية جماعية"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            show_info_message("💬 إرسال رسائل", f"تم إرسال رسائل نصية لـ {len(selected_ids)} مورد بنجاح")

            # إغلاق النافذة الأصلية إذا تم تمرير المرجع
            if parent_dialog:
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إرسال الرسائل: {str(e)}")

    def bulk_edit_category(self, parent_dialog=None):
        """تعديل تصنيف الموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            show_info_message("📂 تعديل التصنيف", f"تم تعديل تصنيف {len(selected_ids)} مورد بنجاح")

            # إغلاق النافذة الأصلية إذا تم تمرير المرجع
            if parent_dialog:
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تعديل التصنيف: {str(e)}")

    def bulk_generate_report(self, parent_dialog=None):
        """إنشاء تقرير مفصل للموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            show_info_message("📊 تقرير مفصل", f"تم إنشاء تقرير مفصل لـ {len(selected_ids)} مورد بنجاح")

            # إغلاق النافذة الأصلية إذا تم تمرير المرجع
            if parent_dialog:
                parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def bulk_update_status(self, parent_dialog=None):
        """تحديث حالة الموردين المحددين"""
        try:
            selected_ids = self.get_selected_suppliers_ids()
            if not selected_ids:
                show_error_message("تحذير", "لا توجد موردين محددين")
                return

            from PyQt5.QtWidgets import QInputDialog
            statuses = ["نشط", "غير نشط", "معلق", "جديد"]
            status, ok = QInputDialog.getItem(self, "تحديث الحالة",
                                            f"اختر الحالة الجديدة لـ {len(selected_ids)} مورد:",
                                            statuses, 0, False)

            if ok and status:
                updated_count = 0
                for supplier_id in selected_ids:
                    try:
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            supplier.status = status
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث حالة المورد {supplier_id}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("تم", f"تم تحديث حالة {updated_count} مورد إلى '{status}' بنجاح")

                # إغلاق النافذة الأصلية إذا تم تمرير المرجع
                if parent_dialog:
                    parent_dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث الحالة: {str(e)}")

    def execute_bulk_operation(self, callback, dialog):
        """تنفيذ عملية مجمعة"""
        try:
            # تمرير مرجع النافذة للدالة
            callback(dialog)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}")
            # إغلاق النافذة في حالة الخطأ أيضاً
            if dialog:
                dialog.reject()



    def toggle_all_columns(self, show):
        """تحديد/إلغاء تحديد جميع الأعمدة"""
        try:
            for checkbox in self.column_checkboxes.values():
                checkbox.setChecked(show)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديد الأعمدة: {str(e)}")

    def apply_column_settings(self, dialog):
        """تطبيق إعدادات الأعمدة"""
        try:
            for i, checkbox in self.column_checkboxes.items():
                self.suppliers_table.setColumnHidden(i, not checkbox.isChecked())

            show_info_message("تم", "تم تطبيق إعدادات الأعمدة بنجاح")
            dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تطبيق الإعدادات: {str(e)}")

    def get_button_style(self, color):
        """إنشاء نمط الأزرار الموحد"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}ee, stop:1 {color});
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {color}aa;
            }}
        """

    def select_all_suppliers(self):
        """تحديد جميع الموردين"""
        try:
            self.suppliers_table.selectAll()
            self.update_selected_count()
            show_info_message("تم", "تم تحديد جميع الموردين")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديد الموردين: {str(e)}")

    def select_visible_suppliers(self):
        """تحديد الموردين المرئيين فقط"""
        try:
            self.suppliers_table.clearSelection()
            for row in range(self.suppliers_table.rowCount()):
                if not self.suppliers_table.isRowHidden(row):
                    self.suppliers_table.selectRow(row)
            self.update_selected_count()
            show_info_message("تم", "تم تحديد الموردين المرئيين")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديد الموردين المرئيين: {str(e)}")

    def clear_suppliers_selection(self):
        """إلغاء تحديد جميع الموردين"""
        try:
            self.suppliers_table.clearSelection()
            self.update_selected_count()
            show_info_message("تم", "تم إلغاء تحديد جميع الموردين")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إلغاء التحديد: {str(e)}")

    def update_selected_count(self):
        """تحديث عداد الموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())
            count = len(selected_rows)
            if hasattr(self, 'selected_count_label'):
                self.selected_count_label.setText(f"📊 عدد الموردين المحددين: {count}")
        except Exception as e:
            print(f"خطأ في تحديث العداد: {str(e)}")

    def bulk_update_status(self, parent_dialog):
        """تحديث حالة الموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_info_message("تنبيه", "يرجى تحديد موردين أولاً")
                return

            # حوار اختيار الحالة الجديدة
            from PyQt5.QtWidgets import QInputDialog
            statuses = ["نشط", "غير نشط", "معلق", "جديد"]
            status, ok = QInputDialog.getItem(parent_dialog, "تحديث الحالة",
                                            "اختر الحالة الجديدة:", statuses, 0, False)

            if ok and status:
                # تحديث الحالة في قاعدة البيانات
                updated_count = 0
                for row in selected_rows:
                    try:
                        supplier_id = int(self.suppliers_table.item(row, 0).text())
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            supplier.status = status
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث المورد في الصف {row}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("تم", f"تم تحديث حالة {updated_count} مورد بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث الحالة: {str(e)}")

    def bulk_update_balance(self, parent_dialog):
        """تعديل رصيد الموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_info_message("تنبيه", "يرجى تحديد موردين أولاً")
                return

            # حوار إدخال المبلغ
            from PyQt5.QtWidgets import QInputDialog
            amount, ok = QInputDialog.getDouble(parent_dialog, "تعديل الرصيد",
                                              "أدخل المبلغ المراد إضافته (أو طرحه إذا كان سالب):",
                                              0, -999999999, 999999999, 2)

            if ok:
                # تحديث الرصيد في قاعدة البيانات
                updated_count = 0
                for row in selected_rows:
                    try:
                        supplier_id = int(self.suppliers_table.item(row, 0).text())
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            supplier.balance = (supplier.balance or 0) + amount
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في تحديث رصيد المورد في الصف {row}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("تم", f"تم تحديث رصيد {updated_count} مورد بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تعديل الرصيد: {str(e)}")

    def bulk_delete_suppliers(self, parent_dialog):
        """حذف الموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_info_message("تنبيه", "يرجى تحديد موردين أولاً")
                return

            # تأكيد الحذف
            reply = show_confirmation_message("تأكيد الحذف",
                                            f"هل أنت متأكد من حذف {len(selected_rows)} مورد؟\n"
                                            "هذا الإجراء لا يمكن التراجع عنه!")

            if reply:
                # حذف الموردين من قاعدة البيانات
                deleted_count = 0
                for row in selected_rows:
                    try:
                        supplier_id = int(self.suppliers_table.item(row, 0).text())
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            self.session.delete(supplier)
                            deleted_count += 1
                    except Exception as e:
                        print(f"خطأ في حذف المورد في الصف {row}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("تم", f"تم حذف {deleted_count} مورد بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حذف الموردين: {str(e)}")



    def bulk_add_notes(self, parent_dialog):
        """إضافة ملاحظات للموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_info_message("تنبيه", "يرجى تحديد موردين أولاً")
                return

            # حوار إدخال الملاحظة
            from PyQt5.QtWidgets import QInputDialog
            note, ok = QInputDialog.getMultiLineText(parent_dialog, "إضافة ملاحظات",
                                                   "أدخل الملاحظة المراد إضافتها:")

            if ok and note.strip():
                # إضافة الملاحظة للموردين المحددين
                updated_count = 0
                for row in selected_rows:
                    try:
                        supplier_id = int(self.suppliers_table.item(row, 0).text())
                        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                        if supplier:
                            existing_notes = supplier.notes or ""
                            if existing_notes:
                                supplier.notes = f"{existing_notes}\n---\n{note}"
                            else:
                                supplier.notes = note
                            updated_count += 1
                    except Exception as e:
                        print(f"خطأ في إضافة ملاحظة للمورد في الصف {row}: {str(e)}")

                self.session.commit()
                self.refresh_data()
                show_info_message("تم", f"تم إضافة الملاحظة لـ {updated_count} مورد بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة الملاحظات: {str(e)}")

    def bulk_send_emails(self, parent_dialog):
        """إرسال إيميلات للموردين المحددين"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                show_info_message("تنبيه", "يرجى تحديد موردين أولاً")
                return

            # جمع الإيميلات
            emails = []
            for row in selected_rows:
                try:
                    supplier_id = int(self.suppliers_table.item(row, 0).text())
                    supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                    if supplier and supplier.email:
                        emails.append(supplier.email)
                except Exception as e:
                    print(f"خطأ في جمع إيميل المورد في الصف {row}: {str(e)}")

            if emails:
                # فتح برنامج الإيميل الافتراضي
                import webbrowser
                email_list = ";".join(emails)
                mailto_url = f"mailto:{email_list}?subject=رسالة من نظام إدارة الموردين"
                webbrowser.open(mailto_url)
                show_info_message("تم", f"تم فتح برنامج الإيميل مع {len(emails)} عنوان")
            else:
                show_info_message("تنبيه", "لا توجد عناوين إيميل صالحة للموردين المحددين")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إرسال الإيميلات: {str(e)}")

    def show_advanced_filters_dialog(self):
        """عرض نافذة الفلاتر المتقدمة الذكية والمتطورة - مطابقة تماماً للعملاء"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                       QFrame, QPushButton, QWidget, QScrollArea,
                                       QGraphicsDropShadowEffect, QGroupBox, QGridLayout,
                                       QLineEdit, QComboBox, QSpinBox, QDateEdit)
            from PyQt5.QtCore import Qt, QDate
            from PyQt5.QtGui import QColor

            dialog = QDialog(self)
            dialog.setWindowTitle("🎯 الفلاتر المتقدمة الذكية والمتطورة")
            dialog.setFixedSize(650, 800)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة - مطابق للعملاء
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #8b5cf6, stop:0.2 #7c3aed, stop:0.4 #6d28d9,
                        stop:0.6 #5b21b6, stop:0.8 #4c1d95, stop:1 #3730a3);
                    border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #8b5cf6, stop:0.5 #7c3aed, stop:1 #6d28d9);
                    border-radius: 0px;
                    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                }
            """)

            # إضافة تأثير الظل للنافذة
            shadow_effect = QGraphicsDropShadowEffect()
            shadow_effect.setBlurRadius(30)
            shadow_effect.setColor(QColor(0, 0, 0, 100))
            shadow_effect.setOffset(0, 10)
            dialog.setGraphicsEffect(shadow_effect)

            layout = QVBoxLayout()
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # شريط العنوان المتطور مثل الشريط الرئيسي - مطابق للعملاء
            title_bar = QFrame()
            title_bar.setFixedHeight(60)
            title_bar.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4c1d95, stop:0.3 #8b5cf6, stop:0.7 #7c3aed, stop:1 #6d28d9);
                    border: none;
                    border-radius: 0px;
                    margin: 0px;
                    padding: 0px;
                }
            """)
            title_bar_layout = QHBoxLayout(title_bar)
            title_bar_layout.setContentsMargins(15, 0, 15, 0)
            title_bar_layout.setSpacing(15)

            # الجانب الأيسر - أزرار التحكم
            left_section = QFrame()
            left_section.setStyleSheet("QFrame { background: transparent; }")
            left_layout = QHBoxLayout(left_section)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(8)

            # زر تصغير
            minimize_btn = QPushButton("🗕")
            minimize_btn.setFixedSize(30, 30)
            minimize_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }
                QPushButton:pressed {
                    background: rgba(255, 255, 255, 0.4);
                    transform: scale(0.95);
                }
            """)
            minimize_btn.clicked.connect(dialog.showMinimized)

            # زر إغلاق
            close_btn = QPushButton("✕")
            close_btn.setFixedSize(30, 30)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border: none;
                    border-radius: 0px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: rgba(220, 38, 38, 0.9);
                    transform: scale(1.1);
                }
                QPushButton:pressed {
                    background: rgba(185, 28, 28, 1.0);
                    transform: scale(0.95);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            left_layout.addWidget(close_btn)
            left_layout.addWidget(minimize_btn)
            left_layout.addStretch()

            # الوسط - عنوان النافذة
            center_section = QFrame()
            center_section.setStyleSheet("QFrame { background: transparent; }")
            center_layout = QHBoxLayout(center_section)
            center_layout.setContentsMargins(0, 0, 0, 0)

            # أيقونة ونص العنوان
            title_icon = QLabel("🎯")
            title_icon.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: white;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    padding: 8px;
                    min-width: 40px;
                    max-width: 40px;
                    text-align: center;
                }
            """)
            title_icon.setAlignment(Qt.AlignCenter)

            title_text = QLabel("الفلاتر المتقدمة الذكية")
            title_text.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    background: transparent;
                    margin-left: 10px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                }
            """)

            center_layout.addWidget(title_icon)
            center_layout.addWidget(title_text)

            # الجانب الأيمن - فارغ
            right_section = QFrame()
            right_section.setStyleSheet("QFrame { background: transparent; }")
            right_layout = QHBoxLayout(right_section)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.addStretch()

            # إضافة الأقسام إلى شريط العنوان (معكوس)
            title_bar_layout.addWidget(left_section, 1)
            title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter)
            title_bar_layout.addWidget(right_section, 1)
            layout.addWidget(title_bar)

            # إضافة وظيفة السحب المتطورة للنافذة - مطابق للعملاء
            def mousePressEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft()
                    dialog.dragging = True
                    # تغيير شكل المؤشر عند بدء السحب
                    dialog.setCursor(Qt.ClosedHandCursor)
                    event.accept()

            def mouseMoveEvent(event):
                if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging:
                    # حساب الموضع الجديد
                    new_position = event.globalPos() - dialog.drag_start_position

                    # التأكد من أن النافذة لا تخرج من حدود الشاشة
                    from PyQt5.QtWidgets import QApplication
                    screen = QApplication.desktop().screenGeometry()
                    dialog_size = dialog.frameGeometry()

                    # تحديد الحد الأدنى والأقصى للموضع
                    min_x = 0
                    min_y = 0
                    max_x = screen.width() - dialog_size.width()
                    max_y = screen.height() - dialog_size.height()

                    # تطبيق القيود
                    x = max(min_x, min(new_position.x(), max_x))
                    y = max(min_y, min(new_position.y(), max_y))

                    dialog.move(x, y)
                    event.accept()

            def mouseReleaseEvent(event):
                if event.button() == Qt.LeftButton:
                    dialog.dragging = False
                    # إعادة شكل المؤشر الطبيعي
                    dialog.setCursor(Qt.ArrowCursor)
                    event.accept()

            # تطبيق الأحداث على شريط العنوان والنافذة
            title_bar.mousePressEvent = mousePressEvent
            title_bar.mouseMoveEvent = mouseMoveEvent
            title_bar.mouseReleaseEvent = mouseReleaseEvent

            # تطبيق الأحداث على النافذة نفسها أيضاً
            dialog.mousePressEvent = mousePressEvent
            dialog.mouseMoveEvent = mouseMoveEvent
            dialog.mouseReleaseEvent = mouseReleaseEvent

            # إضافة متغير لتتبع حالة السحب
            dialog.dragging = False

            # وصف تفصيلي - مطابق للعملاء
            desc_label = QLabel("🔍 استخدم الفلاتر أدناه للبحث المتقدم في قاعدة بيانات الموردين")
            desc_label.setStyleSheet("""
                QLabel {
                    color: #475569;
                    font-size: 14px;
                    font-weight: 500;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.7);
                    border-radius: 10px;
                    border: 2px solid rgba(139, 92, 246, 0.2);
                }
            """)
            desc_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(desc_label)

            # منطقة التمرير للفلاتر - مطابق للعملاء
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 2px solid #e2e8f0;
                    border-radius: 12px;
                    background: white;
                }
                QScrollBar:vertical {
                    background: #f1f5f9;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #8b5cf6;
                    border-radius: 6px;
                    min-height: 20px;
                }
            """)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(15)
            scroll_layout.setContentsMargins(15, 15, 15, 15)

            # مجموعة فلاتر النص المتطورة - مطابق للعملاء
            text_group = QGroupBox("🔤 فلاتر النص")
            text_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    font-size: 16px;
                    color: #374151;
                    border: 2px solid #8b5cf6;
                    border-radius: 12px;
                    margin-top: 10px;
                    padding-top: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(139, 92, 246, 0.1));
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    color: #8b5cf6;
                    background: white;
                    border-radius: 6px;
                }
            """)
            text_layout = QGridLayout()
            text_layout.setSpacing(12)

            # نمط موحد للحقول - مطابق للعملاء
            field_style = """
                QLineEdit {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                }
                QLineEdit:focus {
                    border: 2px solid #8b5cf6;
                    background: #faf5ff;
                }
                QLineEdit:hover {
                    border: 2px solid #c4b5fd;
                }
            """

            label_style = """
                QLabel {
                    font-weight: 600;
                    font-size: 14px;
                    color: #4b5563;
                    padding: 5px;
                }
            """

            # اسم المورد
            name_label = QLabel("👤 اسم المورد:")
            name_label.setStyleSheet(label_style)
            text_layout.addWidget(name_label, 0, 0)
            self.filter_name = QLineEdit()
            self.filter_name.setPlaceholderText("ابحث في أسماء الموردين...")
            self.filter_name.setStyleSheet(field_style)
            text_layout.addWidget(self.filter_name, 0, 1)

            # رقم الهاتف
            phone_label = QLabel("📱 رقم الهاتف:")
            phone_label.setStyleSheet(label_style)
            text_layout.addWidget(phone_label, 1, 0)
            self.filter_phone = QLineEdit()
            self.filter_phone.setPlaceholderText("ابحث في أرقام الهواتف...")
            self.filter_phone.setStyleSheet(field_style)
            text_layout.addWidget(self.filter_phone, 1, 1)

            # الإيميل
            email_label = QLabel("📧 الإيميل:")
            email_label.setStyleSheet(label_style)
            text_layout.addWidget(email_label, 2, 0)
            self.filter_email = QLineEdit()
            self.filter_email.setPlaceholderText("ابحث في الإيميلات...")
            self.filter_email.setStyleSheet(field_style)
            text_layout.addWidget(self.filter_email, 2, 1)

            # العنوان
            address_label = QLabel("📍 العنوان:")
            address_label.setStyleSheet(label_style)
            text_layout.addWidget(address_label, 3, 0)
            self.filter_address = QLineEdit()
            self.filter_address.setPlaceholderText("ابحث في العناوين...")
            self.filter_address.setStyleSheet(field_style)
            text_layout.addWidget(self.filter_address, 3, 1)

            text_group.setLayout(text_layout)
            scroll_layout.addWidget(text_group)

            # مجموعة فلاتر الرصيد المتطورة - مطابق للعملاء
            balance_group = QGroupBox("💰 فلاتر الرصيد")
            balance_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    font-size: 16px;
                    color: #374151;
                    border: 2px solid #f59e0b;
                    border-radius: 12px;
                    margin-top: 10px;
                    padding-top: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(245, 158, 11, 0.1));
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    color: #f59e0b;
                    background: white;
                    border-radius: 6px;
                }
            """)
            balance_layout = QGridLayout()
            balance_layout.setSpacing(12)

            # نوع الرصيد
            balance_type_label = QLabel("📊 نوع الرصيد:")
            balance_type_label.setStyleSheet(label_style)
            balance_layout.addWidget(balance_type_label, 0, 0)
            self.filter_balance_type = QComboBox()
            self.filter_balance_type.addItems(["الكل", "موجب", "سالب", "صفر", "نطاق مخصص"])
            self.filter_balance_type.setStyleSheet("""
                QComboBox {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                    min-width: 150px;
                }
                QComboBox:focus {
                    border: 2px solid #f59e0b;
                    background: #fffbeb;
                }
                QComboBox:hover {
                    border: 2px solid #fbbf24;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 30px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border: 2px solid #f59e0b;
                    width: 8px;
                    height: 8px;
                    border-top: none;
                    border-left: none;
                    transform: rotate(45deg);
                    margin-right: 8px;
                }
                QComboBox QAbstractItemView {
                    border: 2px solid #f59e0b;
                    border-radius: 8px;
                    background: white;
                    selection-background-color: #fbbf24;
                    selection-color: white;
                    font-size: 14px;
                    padding: 5px;
                }
            """)
            balance_layout.addWidget(self.filter_balance_type, 0, 1)

            # الحد الأدنى للرصيد
            min_label = QLabel("⬇️ الحد الأدنى:")
            min_label.setStyleSheet(label_style)
            balance_layout.addWidget(min_label, 1, 0)
            self.filter_balance_min = QSpinBox()
            self.filter_balance_min.setRange(-999999, 999999)
            self.filter_balance_min.setValue(0)
            self.filter_balance_min.setStyleSheet("""
                QSpinBox {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                }
                QSpinBox:focus {
                    border: 2px solid #f59e0b;
                    background: #fffbeb;
                }
                QSpinBox:hover {
                    border: 2px solid #fbbf24;
                }
            """)
            balance_layout.addWidget(self.filter_balance_min, 1, 1)

            # الحد الأقصى للرصيد
            max_label = QLabel("⬆️ الحد الأقصى:")
            max_label.setStyleSheet(label_style)
            balance_layout.addWidget(max_label, 2, 0)
            self.filter_balance_max = QSpinBox()
            self.filter_balance_max.setRange(-999999, 999999)
            self.filter_balance_max.setValue(999999)
            self.filter_balance_max.setStyleSheet("""
                QSpinBox {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                }
                QSpinBox:focus {
                    border: 2px solid #f59e0b;
                    background: #fffbeb;
                }
                QSpinBox:hover {
                    border: 2px solid #fbbf24;
                }
            """)
            balance_layout.addWidget(self.filter_balance_max, 2, 1)

            balance_group.setLayout(balance_layout)
            scroll_layout.addWidget(balance_group)

            # مجموعة فلاتر التاريخ المتطورة - مطابق للعملاء
            date_group = QGroupBox("📅 فلاتر التاريخ")
            date_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    font-size: 16px;
                    color: #374151;
                    border: 2px solid #06b6d4;
                    border-radius: 12px;
                    margin-top: 10px;
                    padding-top: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(6, 182, 212, 0.1));
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    color: #06b6d4;
                    background: white;
                    border-radius: 6px;
                }
            """)
            date_layout = QGridLayout()
            date_layout.setSpacing(12)

            # فلتر الحالة
            status_label = QLabel("🔄 الحالة:")
            status_label.setStyleSheet(label_style)
            date_layout.addWidget(status_label, 0, 0)
            self.filter_status = QComboBox()
            self.filter_status.addItems(["الكل", "نشط", "غير نشط", "معلق", "جديد"])
            self.filter_status.setStyleSheet("""
                QComboBox {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                    min-width: 150px;
                }
                QComboBox:focus {
                    border: 2px solid #06b6d4;
                    background: #ecfeff;
                }
                QComboBox:hover {
                    border: 2px solid #22d3ee;
                }
            """)
            date_layout.addWidget(self.filter_status, 0, 1)

            # فلتر التاريخ من
            start_date_label = QLabel("📅 من تاريخ:")
            start_date_label.setStyleSheet(label_style)
            date_layout.addWidget(start_date_label, 1, 0)
            self.filter_start_date = QDateEdit()
            self.filter_start_date.setDate(QDate.currentDate().addMonths(-1))
            self.filter_start_date.setCalendarPopup(True)
            self.filter_start_date.setStyleSheet("""
                QDateEdit {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                }
                QDateEdit:focus {
                    border: 2px solid #06b6d4;
                    background: #ecfeff;
                }
                QDateEdit:hover {
                    border: 2px solid #22d3ee;
                }
            """)
            date_layout.addWidget(self.filter_start_date, 1, 1)

            # فلتر التاريخ إلى
            end_date_label = QLabel("📅 إلى تاريخ:")
            end_date_label.setStyleSheet(label_style)
            date_layout.addWidget(end_date_label, 2, 0)
            self.filter_end_date = QDateEdit()
            self.filter_end_date.setDate(QDate.currentDate())
            self.filter_end_date.setCalendarPopup(True)
            self.filter_end_date.setStyleSheet("""
                QDateEdit {
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px 12px;
                    font-size: 14px;
                    background: white;
                    color: #374151;
                }
                QDateEdit:focus {
                    border: 2px solid #06b6d4;
                    background: #ecfeff;
                }
                QDateEdit:hover {
                    border: 2px solid #22d3ee;
                }
            """)
            date_layout.addWidget(self.filter_end_date, 2, 1)

            date_group.setLayout(date_layout)
            scroll_layout.addWidget(date_group)

            # إضافة منطقة التمرير إلى التخطيط
            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            # أزرار الحوار المتطورة - مطابق للعملاء
            buttons_frame = QFrame()
            buttons_frame.setFixedHeight(80)
            buttons_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(139, 92, 246, 0.1));
                    border: 2px solid rgba(139, 92, 246, 0.2);
                    border-radius: 15px;
                    margin: 10px;
                }
            """)
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(15)
            buttons_layout.setContentsMargins(20, 15, 20, 15)

            # زر تطبيق الفلاتر
            apply_btn = QPushButton("🔍 تطبيق الفلاتر")
            apply_btn.setFixedHeight(45)
            apply_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #8b5cf6, stop:1 #7c3aed);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 12px 25px;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #7c3aed, stop:1 #6d28d9);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6d28d9, stop:1 #5b21b6);
                    transform: translateY(0px);
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_advanced_filters(dialog))

            # زر إعادة تعيين
            reset_btn = QPushButton("🔄 إعادة تعيين")
            reset_btn.setFixedHeight(45)
            reset_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #f59e0b, stop:1 #d97706);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 12px 25px;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 130px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #d97706, stop:1 #b45309);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #b45309, stop:1 #92400e);
                    transform: translateY(0px);
                }
            """)
            reset_btn.clicked.connect(self.reset_advanced_filters)

            # زر إغلاق
            close_btn = QPushButton("❌ إغلاق")
            close_btn.setFixedHeight(45)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 12px 25px;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:1 #6b7280);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4b5563, stop:1 #374151);
                    transform: translateY(0px);
                }
            """)
            close_btn.clicked.connect(dialog.reject)

            buttons_layout.addWidget(apply_btn)
            buttons_layout.addWidget(reset_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addWidget(buttons_frame)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الفلاتر المتقدمة: {str(e)}")

    def apply_advanced_filters(self, dialog):
        """تطبيق الفلاتر المتقدمة - مطابق للعملاء"""
        try:
            # جمع معايير الفلترة من الحقول الجديدة
            filters = {
                'name': self.filter_name.text().strip(),
                'phone': self.filter_phone.text().strip(),
                'email': self.filter_email.text().strip(),
                'address': self.filter_address.text().strip(),
                'balance_type': self.filter_balance_type.currentText(),
                'min_balance': self.filter_balance_min.value(),
                'max_balance': self.filter_balance_max.value(),
                'status': self.filter_status.currentText(),
                'start_date': self.filter_start_date.date().toPyDate(),
                'end_date': self.filter_end_date.date().toPyDate()
            }

            # تطبيق الفلاتر على الجدول
            filtered_count = 0
            total_rows = self.suppliers_table.rowCount()

            for row in range(total_rows):
                show_row = True

                # فلتر الاسم
                if filters['name']:
                    name_item = self.suppliers_table.item(row, 1)
                    if name_item and filters['name'].lower() not in name_item.text().lower():
                        show_row = False

                # فلتر الهاتف
                if filters['phone'] and show_row:
                    phone_item = self.suppliers_table.item(row, 2)
                    if phone_item and filters['phone'] not in phone_item.text():
                        show_row = False

                # فلتر الإيميل
                if filters['email'] and show_row:
                    email_item = self.suppliers_table.item(row, 3)
                    if email_item and filters['email'].lower() not in email_item.text().lower():
                        show_row = False

                # فلتر العنوان
                if filters['address'] and show_row:
                    address_item = self.suppliers_table.item(row, 4)
                    if address_item and filters['address'].lower() not in address_item.text().lower():
                        show_row = False

                # فلتر الرصيد
                if show_row:
                    balance_item = self.suppliers_table.item(row, 5)
                    if balance_item:
                        try:
                            balance = float(balance_item.text().replace(',', ''))

                            # نطاق الرصيد
                            if balance < filters['min_balance'] or balance > filters['max_balance']:
                                show_row = False

                            # نوع الرصيد
                            if filters['balance_type'] == "رصيد موجب" and balance <= 0:
                                show_row = False
                            elif filters['balance_type'] == "رصيد سالب" and balance >= 0:
                                show_row = False
                            elif filters['balance_type'] == "رصيد صفر" and balance != 0:
                                show_row = False
                        except:
                            pass

                # فلتر الحالة
                if filters['status'] != "الكل" and show_row:
                    status_item = self.suppliers_table.item(row, 7)
                    if status_item and status_item.text() != filters['status']:
                        show_row = False

                # إخفاء/إظهار الصف
                self.suppliers_table.setRowHidden(row, not show_row)
                if show_row:
                    filtered_count += 1

            show_info_message("تم", f"تم تطبيق الفلاتر. عدد النتائج: {filtered_count} من أصل {total_rows}")
            dialog.accept()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تطبيق الفلاتر: {str(e)}")

    def reset_advanced_filters(self):
        """إعادة تعيين الفلاتر المتقدمة - مطابق للعملاء"""
        try:
            # إعادة تعيين جميع الحقول الجديدة
            self.filter_name.clear()
            self.filter_phone.clear()
            self.filter_email.clear()
            self.filter_address.clear()
            self.filter_balance_type.setCurrentIndex(0)
            self.filter_balance_min.setValue(0)
            self.filter_balance_max.setValue(999999)
            self.filter_status.setCurrentIndex(0)
            self.filter_start_date.setDate(QDate.currentDate().addMonths(-1))
            self.filter_end_date.setDate(QDate.currentDate())

            # إظهار جميع الصفوف
            for row in range(self.suppliers_table.rowCount()):
                self.suppliers_table.setRowHidden(row, False)

            show_info_message("تم", "تم إعادة تعيين جميع الفلاتر")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إعادة تعيين الفلاتر: {str(e)}")

    def populate_table(self, suppliers):
        """ملء جدول الموردين المتطور بالبيانات المحسنة والمتقدمة"""
        print("📊 ملء الجدول بالبيانات المتطورة والمحسنة...")
        self.suppliers_table.setRowCount(0)

        for row, supplier in enumerate(suppliers):
            self.suppliers_table.insertRow(row)

            # خطوط متطورة وموحدة - حجم مناسب مع Bold
            unified_font = QFont("Segoe UI", 11, QFont.Bold)  # خط مناسب مع Bold
            unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5)
            unified_font.setStyleHint(QFont.SansSerif)
            unified_font.setWeight(QFont.Bold)

            unified_bold_font = QFont("Segoe UI", 11, QFont.Bold)  # نفس الحجم للعريض
            unified_bold_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.8)
            unified_bold_font.setStyleHint(QFont.SansSerif)
            unified_bold_font.setWeight(QFont.ExtraBold)

            # 1. الرقم المسلسل مع تصميم متطور ومميز وأيقونة حسب الحالة
            # اختيار الأيقونة حسب الرصيد والحالة
            if supplier.balance > 10000:
                icon = "💎"  # VIP - مبلغ كبير جداً
            elif supplier.balance > 1000:
                icon = "⭐"  # ممتاز - مبلغ كبير
            elif supplier.balance > 0:
                icon = "🟢"  # نشط - مبلغ موجب
            elif supplier.balance == 0:
                icon = "✅"  # عادي - بدون رصيد
            elif supplier.balance > -1000:
                icon = "🟡"  # مدين بسيط
            elif supplier.balance > -5000:
                icon = "⚠️"  # تحذير - مدين متوسط
            else:
                icon = "🚨"  # خطر - مدين كبير

            id_display = f"{supplier.id} {icon}"
            id_item = QTableWidgetItem(id_display)
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(unified_bold_font)

            # لون أسود موحد وعريض لجميع أرقام ID
            id_item.setForeground(QColor("#000000"))  # أسود عريض موحد
            id_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

            # تحديد نوع المورد في التلميح فقط
            if supplier.id <= 10:
                id_item.setToolTip(f"👑 مورد مؤسس رقم: {supplier.id}")
            elif supplier.id <= 50:
                id_item.setToolTip(f"⭐ مورد مبكر رقم: {supplier.id}")
            else:
                id_item.setToolTip(f"🆔 رقم المورد: {supplier.id}")

            self.suppliers_table.setItem(row, 0, id_item)

            # 2. اسم المورد مع تصميم جذاب ومتطور
            # تحسين عرض الاسم
            display_name = supplier.name.title() if supplier.name else "غير محدد"
            if len(display_name) > 20:
                display_name = display_name[:17] + "..."

            name_item = QTableWidgetItem(display_name)
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setFont(unified_bold_font)

            # لون أسود موحد وعريض لجميع الأسماء
            name_item.setForeground(QColor("#000000"))  # أسود عريض موحد
            name_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

            # تحديد نوع المورد في التلميح فقط
            if supplier.balance > 10000:
                name_item.setToolTip(f"👑 مورد VIP: {supplier.name}\n💰 رصيد عالي: {supplier.balance:,.0f} ر.س")
            elif supplier.balance > 0:
                name_item.setToolTip(f"✅ مورد نشط: {supplier.name}\n💰 رصيد موجب: {supplier.balance:,.0f} ر.س")
            elif supplier.balance < 0:
                name_item.setToolTip(f"⚠️ مورد مدين: {supplier.name}\n💸 مبلغ مستحق: {abs(supplier.balance):,.0f} ر.س")
            else:
                name_item.setToolTip(f"👤 مورد عادي: {supplier.name}\n💰 رصيد صفر")

            self.suppliers_table.setItem(row, 1, name_item)

            # 3. الهاتف مع تصميم احترافي ومتطور
            if supplier.phone:
                # تنسيق رقم الهاتف بشكل جميل
                phone_clean = supplier.phone.replace(" ", "").replace("-", "")
                if phone_clean.startswith("966"):
                    phone_display = f"+966 {phone_clean[3:6]} {phone_clean[6:9]} {phone_clean[9:]}"
                elif phone_clean.startswith("05"):
                    phone_display = f"0{phone_clean[1:3]} {phone_clean[3:6]} {phone_clean[6:]}"
                else:
                    phone_display = supplier.phone

                phone_item = QTableWidgetItem(phone_display)
                phone_item.setFont(unified_bold_font)

                # لون أسود موحد لجميع أرقام الهواتف
                phone_item.setForeground(QColor("#000000"))  # أسود موحد
                phone_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الرقم في التلميح فقط
                if phone_clean.startswith("966") or phone_clean.startswith("00966"):
                    phone_item.setToolTip(f"🌍 رقم دولي: {phone_display}\n📞 انقر نقرتين للاتصال\n📱 واتساب متاح")
                elif phone_clean.startswith("05"):
                    phone_item.setToolTip(f"📱 جوال سعودي: {phone_display}\n📞 انقر نقرتين للاتصال\n💬 واتساب متاح")
                else:
                    phone_item.setToolTip(f"☎️ هاتف: {phone_display}\n📞 انقر نقرتين للاتصال")
            else:
                phone_item = QTableWidgetItem("📵 غير متوفر")
                phone_item.setFont(unified_bold_font)  # خط عريض
                phone_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                phone_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح جداً
                phone_item.setToolTip("📵 لا يوجد رقم هاتف مسجل\n💡 يمكن إضافة رقم من خلال التعديل")

            phone_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 2, phone_item)

            # 4. البريد الإلكتروني مع تصميم متطور
            if supplier.email:
                email_text = supplier.email
                if len(email_text) > 25:
                    email_display = email_text[:22] + "..."
                else:
                    email_display = email_text

                email_item = QTableWidgetItem(email_display)
                email_item.setFont(unified_bold_font)  # خط عريض أسود

                # تحديد نوع البريد الإلكتروني - جميع الإيميلات بلون أسود موحد وعريض
                # تم توحيد لون جميع الإيميلات إلى الأسود العريض لتجنب الالتباس
                email_item.setForeground(QColor("#000000"))  # أسود عريض موحد لجميع الإيميلات
                email_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة موحدة

                # تحديد نوع الإيميل في التلميح فقط
                if "@gmail.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Gmail: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@outlook.com" in supplier.email.lower() or "@hotmail.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Outlook: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                elif "@yahoo.com" in supplier.email.lower():
                    email_item.setToolTip(f"📧 Yahoo: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
                else:
                    email_item.setToolTip(f"📧 إيميل: {supplier.email}\n💡 انقر نقرتين لإرسال إيميل")
            else:
                email_item = QTableWidgetItem("📧 غير متوفر")
                email_item.setFont(unified_bold_font)  # خط عريض
                email_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                email_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                email_item.setToolTip("📧 لا يوجد بريد إلكتروني\n💡 يمكن إضافة إيميل من خلال التعديل")

            email_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 3, email_item)

            # 5. العنوان
            address_text = supplier.address or "غير متوفر"
            if len(address_text) > 20:  # تقصير العناوين الطويلة
                address_display = address_text[:17] + "..."
            else:
                address_display = address_text

            address_item = QTableWidgetItem(address_display)
            address_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if supplier.address:
                address_item.setFont(unified_bold_font)  # خط عريض أسود
                address_item.setForeground(QColor("#000000"))  # أسود عريض
                address_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                address_item.setToolTip(f"📍 العنوان: {supplier.address}\n💡 انقر نقرتين لفتح الخريطة")
            else:
                address_item.setFont(unified_bold_font)  # خط عريض
                address_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                address_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                address_item.setToolTip("📍 لا يوجد عنوان مسجل\n💡 يمكن إضافة عنوان من خلال التعديل")
            self.suppliers_table.setItem(row, 4, address_item)

            # 6. الرصيد مع تصميم متطور وجذاب (بدون كسور وبدون ر.س)
            balance_text = f"{supplier.balance:,.0f}"
            balance_item = QTableWidgetItem(balance_text)
            balance_item.setTextAlignment(Qt.AlignCenter)
            # تطبيق خط موحد ثابت للرصيد
            balance_item.setFont(unified_bold_font)  # نفس الخط الموحد العريض

            # تلوين الرصيد حسب القيمة مع ألوان واضحة ومميزة
            if supplier.balance > 10000:
                # رصيد عالي جداً - أخضر ذهبي مميز
                balance_item.setForeground(QColor("#1a5f3f"))  # أخضر داكن
                balance_item.setBackground(QColor("#d4edda"))  # أخضر فاتح
                balance_item.setToolTip(f"💰 رصيد ممتاز: {balance_text}\n⭐ مورد VIP")
            elif supplier.balance > 0:
                # رصيد موجب - أخضر واضح
                balance_item.setForeground(QColor("#155724"))  # أخضر داكن
                balance_item.setBackground(QColor("#d1ecf1"))  # أخضر فاتح جداً
                balance_item.setToolTip(f"💰 رصيد موجب: {balance_text}\n✅ مورد نشط")
            elif supplier.balance < -5000:
                # رصيد سالب عالي - أحمر قوي
                balance_item.setForeground(QColor("#721c24"))  # أحمر داكن جداً
                balance_item.setBackground(QColor("#f8d7da"))  # أحمر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين عالي: {balance_text}\n🚨 يحتاج متابعة")
            elif supplier.balance < 0:
                # رصيد سالب - أحمر واضح
                balance_item.setForeground(QColor("#856404"))  # برتقالي داكن
                balance_item.setBackground(QColor("#fff3cd"))  # أصفر فاتح
                balance_item.setToolTip(f"💰 رصيد مدين: {balance_text}\n⚠️ يحتاج متابعة")
            else:
                # رصيد صفر - رمادي
                balance_item.setForeground(QColor("#495057"))  # رمادي داكن
                balance_item.setBackground(QColor("#e9ecef"))  # رمادي فاتح
                balance_item.setToolTip(f"💰 رصيد صفر: {balance_text}\n➖ لا يوجد رصيد")

            self.suppliers_table.setItem(row, 5, balance_item)

            # 7. الملاحظات مع عرض أفضل
            notes_text = supplier.notes or "غير متوفر"
            if len(notes_text) > 25:  # زيادة الحد الأقصى للنص
                notes_display = notes_text[:22] + "..."
            else:
                notes_display = notes_text

            notes_item = QTableWidgetItem(notes_display)
            notes_item.setTextAlignment(Qt.AlignCenter)  # توسيط النص
            if supplier.notes:
                notes_item.setFont(unified_bold_font)  # خط عريض أسود
                notes_item.setForeground(QColor("#000000"))  # أسود عريض
                notes_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                notes_item.setToolTip(supplier.notes)  # النص الكامل في التلميح
            else:
                notes_item.setFont(unified_bold_font)  # خط عريض
                notes_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                notes_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                notes_item.setToolTip("📝 لا توجد ملاحظات\n💡 يمكن إضافة ملاحظات من خلال التعديل")
            self.suppliers_table.setItem(row, 6, notes_item)

            # 8. الحالة مع تصميم احترافي محسن
            # تحديد الحالة بناءً على الرصيد مع إضافة حالة "عادي"
            if supplier.balance > 0:
                status_text = "🟢 نشط"
                status_color = QColor("#38a169")  # أخضر للنشط
                status_bg = QColor("#c6f6d5")     # خلفية خضراء فاتحة
            elif supplier.balance == 0:
                status_text = "⚫ عادى"  # النص مع علامة سوداء
                status_color = QColor("#000000")  # أسود للعادى
                status_bg = QColor("#f8f9fa")     # خلفية رمادية فاتحة
            else:
                status_text = "🔴 مدين"
                status_color = QColor("#e53e3e")  # أحمر للمدين
                status_bg = QColor("#fed7d7")     # خلفية حمراء فاتحة

            # إنشاء عنصر الحالة مع تنسيق موحد
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)

            # تطبيق خط موحد للحالة
            status_item.setFont(unified_bold_font)  # نفس الخط الموحد العريض

            # تطبيق الألوان
            status_item.setForeground(status_color)
            status_item.setBackground(status_bg)

            # إضافة تلميح للحالة
            if supplier.balance > 0:
                status_item.setToolTip(f"🟢 مورد نشط\n💰 الرصيد: {supplier.balance:,.0f}")
            elif supplier.balance == 0:
                status_item.setToolTip(f"⚫ مورد عادى\n💰 الرصيد: صفر")
            else:
                status_item.setToolTip(f"🔴 مورد مدين\n💰 المبلغ المستحق: {abs(supplier.balance):,.0f}")

            self.suppliers_table.setItem(row, 7, status_item)

            # 9. التاريخ
            if hasattr(supplier, 'created_at') and supplier.created_at:
                date_text = supplier.created_at.strftime('%Y-%m-%d')
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#000000"))  # أسود عريض
                date_item.setBackground(QColor("#f8f9fa"))  # خلفية رمادية فاتحة
                date_item.setToolTip(f"📅 تاريخ الإضافة: {date_text}")
            else:
                date_text = "غير متوفر"
                date_item = QTableWidgetItem(date_text)
                date_item.setForeground(QColor("#DC2626"))  # أحمر للغير متوفر
                date_item.setBackground(QColor("#FEE2E2"))  # أحمر فاتح
                date_item.setToolTip("📅 لا يوجد تاريخ إضافة مسجل")

            date_item.setTextAlignment(Qt.AlignCenter)
            date_item.setFont(unified_bold_font)  # خط عريض أسود
            self.suppliers_table.setItem(row, 8, date_item)

        # تطبيق الأعراض الجديدة مباشرة مع فرض التطبيق
        print("📏 فرض تطبيق الأعراض الجديدة...")

        # أولاً: تعيين الأعمدة الثابتة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(8, header.Fixed)  # التاريخ ثابت

        # ثانياً: تطبيق الأعراض المحسنة مع عرض أكبر للتاريخ
        self.suppliers_table.setColumnWidth(0, 120)  # الرقم - أكبر
        self.suppliers_table.setColumnWidth(1, 150)  # الاسم
        self.suppliers_table.setColumnWidth(2, 220)  # الهاتف
        self.suppliers_table.setColumnWidth(3, 180)  # الإيميل
        self.suppliers_table.setColumnWidth(4, 150)  # العنوان
        self.suppliers_table.setColumnWidth(5, 155)  # الرصيد - أصغر قليلاً
        self.suppliers_table.setColumnWidth(6, 105)  # الملاحظات - أكبر قليلاً لاستيعاب النص
        self.suppliers_table.setColumnWidth(7, 120)  # الحالة
        self.suppliers_table.setColumnWidth(8, 154)  # التاريخ - أكبر بكثير (استغلال المساحة المحررة)

        print("✅ تم فرض تطبيق الأعراض الجديدة")

        print("✅ تم ملء الجدول بالبيانات المحسنة والمطورة")

        # تطبيق التحسينات النهائية بعد ملء البيانات
        self.apply_suppliers_final_enhancements()

    def apply_suppliers_final_enhancements(self):
        """تطبيق التحسينات النهائية على جدول الموردين"""
        try:
            print("🎨 تطبيق التحسينات النهائية على جدول الموردين...")

            # تطبيق ألوان الرصيد مرة أخيرة للتأكد
            self.apply_suppliers_balance_colors()

            # تطبيق الأعراض الصحيحة
            self.apply_suppliers_correct_widths()

            # تحديث ألوان العناوين لتطابق العملاء
            self.update_suppliers_header_colors()

            # تطبيق الإطار الأسود
            self.force_suppliers_black_border_style()

            print("✅ تم تطبيق التحسينات النهائية على جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التحسينات النهائية: {str(e)}")

    def apply_suppliers_balance_colors(self):
        """تطبيق ألوان الرصيد على جدول الموردين"""
        try:
            print("🎨 تطبيق ألوان الرصيد على جدول الموردين...")

            for row in range(self.suppliers_table.rowCount()):
                balance_item = self.suppliers_table.item(row, 5)  # عمود الرصيد
                if balance_item:
                    balance_text = balance_item.text().replace(',', '')
                    try:
                        balance = float(balance_text)

                        # تطبيق الألوان حسب قيمة الرصيد
                        if balance > 10000:
                            # رصيد عالي جداً - أخضر ذهبي
                            balance_item.setForeground(QColor("#1a5f3f"))
                            balance_item.setBackground(QColor("#d4edda"))
                            print(f"🟢 أخضر ذهبي للصف {row} (رصيد: {balance})")
                        elif balance > 0:
                            # رصيد موجب - أخضر
                            balance_item.setForeground(QColor("#155724"))
                            balance_item.setBackground(QColor("#d1ecf1"))
                            print(f"🟢 أخضر للصف {row} (رصيد: {balance})")
                        elif balance < -5000:
                            # رصيد سالب عالي - أحمر قوي
                            balance_item.setForeground(QColor("#721c24"))
                            balance_item.setBackground(QColor("#f8d7da"))
                            print(f"🔴 أحمر قوي للصف {row} (رصيد: {balance})")
                        elif balance < 0:
                            # رصيد سالب - برتقالي
                            balance_item.setForeground(QColor("#856404"))
                            balance_item.setBackground(QColor("#fff3cd"))
                            print(f"🟠 برتقالي للصف {row} (رصيد: {balance})")
                        else:
                            # رصيد صفر - رمادي
                            balance_item.setForeground(QColor("#495057"))
                            balance_item.setBackground(QColor("#e9ecef"))
                            print(f"⚪ رمادي للصف {row} (رصيد: {balance})")

                    except ValueError:
                        pass

            print("✅ تم تطبيق ألوان الرصيد على جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق ألوان الرصيد: {str(e)}")

    def apply_suppliers_correct_widths(self):
        """تطبيق الأعراض الصحيحة لجدول الموردين مطابقة للعملاء"""
        try:
            print("🔧 تطبيق الأعراض الصحيحة لجدول الموردين...")

            # تطبيق الأعراض المحسنة مع عرض أكبر للتاريخ
            self.suppliers_table.setColumnWidth(0, 120)  # الرقم - أكبر
            self.suppliers_table.setColumnWidth(1, 150)  # الاسم
            self.suppliers_table.setColumnWidth(2, 220)  # الهاتف
            self.suppliers_table.setColumnWidth(3, 180)  # الإيميل
            self.suppliers_table.setColumnWidth(4, 150)  # العنوان
            self.suppliers_table.setColumnWidth(5, 155)  # الرصيد - أصغر قليلاً
            self.suppliers_table.setColumnWidth(6, 105)  # الملاحظات - أكبر قليلاً لاستيعاب النص
            self.suppliers_table.setColumnWidth(7, 120)  # الحالة
            self.suppliers_table.setColumnWidth(8, 154)  # التاريخ - أكبر بكثير (استغلال المساحة المحررة)

            # فرض التحديث
            self.suppliers_table.update()
            self.suppliers_table.repaint()

            # تطبيق ارتفاع الصفوف المحسن - أصغر
            for row in range(self.suppliers_table.rowCount()):
                self.suppliers_table.setRowHeight(row, 40)  # ارتفاع أصغر للصفوف

            print("✅ تم تطبيق الأعراض الصحيحة لجدول الموردين بنجاح!")
            print(f"📏 عرض الرقم: {self.suppliers_table.columnWidth(0)}px")
            print(f"📏 عرض الاسم: {self.suppliers_table.columnWidth(1)}px")
            print(f"📏 عرض الهاتف: {self.suppliers_table.columnWidth(2)}px")
            print(f"📏 عرض الإيميل: {self.suppliers_table.columnWidth(3)}px")
            print(f"📏 عرض العنوان: {self.suppliers_table.columnWidth(4)}px")
            print(f"📏 عرض الرصيد: {self.suppliers_table.columnWidth(5)}px")
            print(f"📏 عرض الملاحظات: {self.suppliers_table.columnWidth(6)}px")
            print(f"📏 عرض الحالة: {self.suppliers_table.columnWidth(7)}px")
            print(f"📏 عرض التاريخ: {self.suppliers_table.columnWidth(8)}px")
            print(f"📏 ارتفاع الصفوف: 40px")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الأعراض: {str(e)}")

    def force_suppliers_black_border_style(self):
        """تطبيق إطار أسود بسيط فقط مع الحفاظ على التصميم الأصلي للموردين"""
        try:
            # الحصول على التنسيق الحالي وتعديل الإطار فقط
            current_style = self.suppliers_table.styleSheet()

            # إضافة الإطار الأسود فقط
            black_border_addition = """
                QTableWidget {
                    border: 3px solid #000000 !important;
                }
            """

            # دمج التنسيق الحالي مع الإطار الأسود
            combined_style = current_style + black_border_addition
            self.suppliers_table.setStyleSheet(combined_style)

            print("✅ تم تطبيق الإطار الأسود البسيط للموردين")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإطار الأسود للموردين: {str(e)}")

    def update_suppliers_header_colors(self):
        """تحديث ألوان عناوين جدول الموردين لتطابق العملاء"""
        try:
            print("🎨 تحديث ألوان عناوين جدول الموردين...")

            header = self.suppliers_table.horizontalHeader()

            # تطبيق نفس الخط والارتفاع من جدول العملاء
            header_font = QFont("Segoe UI", 15, QFont.Bold)  # خط متوسط ومميز
            header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.0)  # تباعد الأحرف
            header.setFont(header_font)

            # تطبيق نفس الارتفاع من جدول العملاء
            header.setFixedHeight(55)  # نفس ارتفاع العملاء

            # تطبيق نفس ألوان العناوين من جدول العملاء
            header.setStyleSheet("""
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    color: white;
                    border: 3px solid #5a67d8;
                    border-bottom: 5px solid #4c51bf;
                    padding: 15px 8px;
                    font-weight: bold;
                    font-size: 14px;
                    text-align: center;
                    min-height: 35px;
                    max-height: 55px;
                    letter-spacing: 1.0px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                    border: 3px solid #4c51bf;
                    border-bottom: 5px solid #3c366b;
                }
                QHeaderView::section:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4c51bf, stop:0.5 #553c9a, stop:1 #c084fc);
                    border: 3px solid #3c366b;
                    border-bottom: 5px solid #2d3748;
                }
            """)

            print("✅ تم تحديث ألوان عناوين جدول الموردين بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تحديث ألوان العناوين: {str(e)}")

    # ==================== دوال تحديث البيانات ====================

    def refresh_data(self):
        """تحديث البيانات مع الحفاظ الكامل على التنسيق والخط الموحد والبولد - مطابق للعملاء"""
        try:
            print("🔄 بدء التحديث مع الحفاظ الكامل على التنسيق...")

            # الحصول على جميع الموردين من قاعدة البيانات
            suppliers = self.session.query(Supplier).all()

            # تحديث الجدول مع الحفاظ على التنسيق
            self.populate_table(suppliers)

            # تهيئة الأزرار - تظهر مفعلة عند البدء مثل العملاء
            self.initialize_button_states()



            print("✅ تم التحديث بنجاح مع الحفاظ على جميع التحسينات!")

        except Exception as e:
            print(f"❌ خطأ في التحديث: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    # ==================== دوال إدارة الموردين الأساسية ====================

    def add_supplier(self):
        """إضافة مورد جديد"""
        from ui.dialogs import SupplierDialog
        dialog = SupplierDialog(self)
        if dialog.exec_() == 1:
            self.refresh_data()

    def quick_add_supplier(self):
        """إضافة مورد سريعة - نموذج مبسط"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QFormLayout

            # إنشاء نافذة الإضافة السريعة
            dialog = QDialog(self)
            dialog.setWindowTitle("➕ إضافة مورد سريع")
            dialog.setFixedSize(450, 350)
            dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

            # تطبيق نمط متطور
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    border: 3px solid #4f46e5;
                    border-radius: 15px;
                }
                QLabel {
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                }
                QLineEdit {
                    background: rgba(255, 255, 255, 0.9);
                    border: 2px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 10px;
                    font-size: 14px;
                    color: #374151;
                }
                QLineEdit:focus {
                    border: 2px solid #06b6d4;
                    background: white;
                }
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #059669, stop:1 #047857);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #047857, stop:1 #065f46);
                }
            """)

            layout = QVBoxLayout()
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # عنوان النافذة
            title_label = QLabel("➕ إضافة مورد جديد - نموذج سريع")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: white;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    text-align: center;
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # نموذج الحقول
            form_frame = QFrame()
            form_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 15px;
                }
            """)
            form_layout = QFormLayout(form_frame)
            form_layout.setSpacing(12)

            # الحقول الأساسية
            self.quick_name = QLineEdit()
            self.quick_name.setPlaceholderText("أدخل اسم المورد...")
            form_layout.addRow("👤 اسم المورد:", self.quick_name)

            self.quick_phone = QLineEdit()
            self.quick_phone.setPlaceholderText("أدخل رقم الهاتف...")
            form_layout.addRow("📱 رقم الهاتف:", self.quick_phone)

            self.quick_email = QLineEdit()
            self.quick_email.setPlaceholderText("أدخل البريد الإلكتروني...")
            form_layout.addRow("📧 البريد الإلكتروني:", self.quick_email)

            self.quick_address = QLineEdit()
            self.quick_address.setPlaceholderText("أدخل العنوان...")
            form_layout.addRow("📍 العنوان:", self.quick_address)

            layout.addWidget(form_frame)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("💾 حفظ")
            save_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                }
            """)
            save_btn.clicked.connect(lambda: self.save_quick_supplier(dialog))

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            buttons_layout.addStretch()
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)

            # تركيز على حقل الاسم
            self.quick_name.setFocus()

            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الإضافة السريعة: {str(e)}")

    def save_quick_supplier(self, dialog):
        """حفظ المورد من النموذج السريع"""
        try:
            # التحقق من الحقول المطلوبة
            name = self.quick_name.text().strip()
            if not name:
                show_error_message("خطأ", "اسم المورد مطلوب")
                return

            # إنشاء مورد جديد
            new_supplier = Supplier(
                name=name,
                phone=self.quick_phone.text().strip() or None,
                email=self.quick_email.text().strip() or None,
                address=self.quick_address.text().strip() or None,
                balance=0.0,
                status='نشط',
                notes='تم إضافته عبر النموذج السريع'
            )

            # حفظ في قاعدة البيانات
            self.session.add(new_supplier)
            self.session.commit()

            # تحديث الجدول
            self.refresh_data()

            # إغلاق النافذة
            dialog.accept()

            show_info_message("✅ تم الحفظ", f"تم إضافة المورد '{name}' بنجاح!")

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def import_from_file(self):
        """استيراد الموردين من ملف Excel أو CSV"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QProgressDialog
            from PyQt5.QtCore import QCoreApplication
            import csv
            import json

            # اختيار نوع الملف
            file_path, file_type = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف للاستيراد",
                "",
                "CSV Files (*.csv);;Excel Files (*.xlsx *.xls);;JSON Files (*.json);;All Files (*.*)"
            )

            if not file_path:
                return

            # شريط التقدم
            progress = QProgressDialog("جاري استيراد البيانات...", "إلغاء", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            imported_count = 0
            errors = []

            try:
                if file_path.lower().endswith('.csv'):
                    # استيراد من CSV
                    with open(file_path, 'r', encoding='utf-8-sig') as file:
                        reader = csv.DictReader(file)
                        rows = list(reader)
                        total_rows = len(rows)

                        for i, row in enumerate(rows):
                            if progress.wasCanceled():
                                break

                            progress.setValue(int((i / total_rows) * 100))
                            QCoreApplication.processEvents()

                            try:
                                # تنظيف البيانات
                                name = row.get('اسم المورد', '').strip() or row.get('name', '').strip()
                                phone = row.get('رقم الهاتف', '').strip() or row.get('phone', '').strip()
                                email = row.get('البريد الإلكتروني', '').strip() or row.get('email', '').strip()
                                address = row.get('العنوان', '').strip() or row.get('address', '').strip()
                                balance = float(row.get('الرصيد', 0) or row.get('balance', 0) or 0)
                                status = row.get('الحالة', 'نشط').strip() or row.get('status', 'نشط').strip()
                                notes = row.get('الملاحظات', '').strip() or row.get('notes', '').strip()

                                if not name:
                                    errors.append(f"الصف {i+1}: اسم المورد مطلوب")
                                    continue

                                # التحقق من عدم وجود مورد بنفس الاسم
                                existing = self.session.query(Supplier).filter_by(name=name).first()
                                if existing:
                                    errors.append(f"الصف {i+1}: المورد '{name}' موجود بالفعل")
                                    continue

                                # إنشاء مورد جديد
                                supplier = Supplier(
                                    name=name,
                                    phone=phone or None,
                                    email=email or None,
                                    address=address or None,
                                    balance=balance,
                                    status=status,
                                    notes=notes or f'تم استيراده من {file_path}'
                                )

                                self.session.add(supplier)
                                imported_count += 1

                            except Exception as e:
                                errors.append(f"الصف {i+1}: {str(e)}")

                elif file_path.lower().endswith('.json'):
                    # استيراد من JSON
                    with open(file_path, 'r', encoding='utf-8') as file:
                        data = json.load(file)

                        if isinstance(data, list):
                            total_items = len(data)

                            for i, item in enumerate(data):
                                if progress.wasCanceled():
                                    break

                                progress.setValue(int((i / total_items) * 100))
                                QCoreApplication.processEvents()

                                try:
                                    name = item.get('name', '').strip()
                                    if not name:
                                        errors.append(f"العنصر {i+1}: اسم المورد مطلوب")
                                        continue

                                    # التحقق من عدم وجود مورد بنفس الاسم
                                    existing = self.session.query(Supplier).filter_by(name=name).first()
                                    if existing:
                                        errors.append(f"العنصر {i+1}: المورد '{name}' موجود بالفعل")
                                        continue

                                    supplier = Supplier(
                                        name=name,
                                        phone=item.get('phone'),
                                        email=item.get('email'),
                                        address=item.get('address'),
                                        balance=float(item.get('balance', 0)),
                                        status=item.get('status', 'نشط'),
                                        notes=item.get('notes', f'تم استيراده من {file_path}')
                                    )

                                    self.session.add(supplier)
                                    imported_count += 1

                                except Exception as e:
                                    errors.append(f"العنصر {i+1}: {str(e)}")
                        else:
                            errors.append("ملف JSON يجب أن يحتوي على قائمة من الموردين")

                else:
                    show_error_message("خطأ", "نوع الملف غير مدعوم. يرجى استخدام CSV أو JSON")
                    return

                # حفظ التغييرات
                if imported_count > 0:
                    self.session.commit()
                    self.refresh_data()

                progress.setValue(100)
                progress.close()

                # عرض النتائج
                result_message = f"✅ تم استيراد {imported_count} مورد بنجاح!"

                if errors:
                    error_summary = "\n".join(errors[:10])  # أول 10 أخطاء
                    if len(errors) > 10:
                        error_summary += f"\n... و {len(errors) - 10} خطأ آخر"

                    result_message += f"\n\n⚠️ الأخطاء ({len(errors)}):\n{error_summary}"

                show_info_message("نتائج الاستيراد", result_message)

            except Exception as e:
                progress.close()
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء قراءة الملف: {str(e)}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الاستيراد: {str(e)}")

    def edit_supplier(self):
        """تعديل مورد محدد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        # استخراج الرقم من النص الذي يحتوي على أيقونات
        id_text = self.suppliers_table.item(selected_row, 0).text()
        supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        from ui.dialogs import SupplierDialog
        dialog = SupplierDialog(self, supplier)
        if dialog.exec_() == 1:
            self.refresh_data()

    def delete_supplier(self):
        """حذف مورد محدد"""
        selected_row = self.suppliers_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مورد من القائمة")
            return

        # استخراج الرقم من النص الذي يحتوي على أيقونات
        id_text = self.suppliers_table.item(selected_row, 0).text()
        supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
        supplier = self.session.query(Supplier).get(supplier_id)

        if not supplier:
            show_error_message("خطأ", "لم يتم العثور على المورد")
            return

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المورد '{supplier.name}'؟"):
            try:
                self.session.delete(supplier)
                self.session.commit()
                show_info_message("تم", "تم حذف المورد بنجاح")
                self.refresh_data()
            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف المورد: {str(e)}")

    def adjust_balance(self):
        """تعديل رصيد المورد المحدد - مطابق للعملاء"""
        try:
            # التحقق من وجود مورد محدد
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                show_error_message("تحذير", "الرجاء اختيار مورد من الجدول أولاً")
                return

            # الحصول على معرف المورد
            try:
                id_text = self.suppliers_table.item(current_row, 0).text()
                supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
            except (ValueError, AttributeError):
                show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                return

            # الحصول على بيانات المورد
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # استخدام نفس نظام العملاء
            dialog = SupplierBalanceAdjustmentDialog(self, supplier)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # تحديث المبلغ المستحق
                    success, new_balance = update_supplier_balance(
                        self.session,
                        supplier_id,
                        data['amount'],
                        data['operation']
                    )

                    if success:
                        show_info_message(
                            "تم",
                            f"تم تحديث المبلغ المستحق للمورد {supplier.name} بنجاح.\nالمبلغ الجديد: {new_balance:.2f}"
                        )
                        self.refresh_data()
                    else:
                        show_error_message("خطأ", "حدث خطأ أثناء تحديث المبلغ المستحق")

        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ في تعديل الرصيد: {str(e)}")

    def manage_attachments(self):
        """إدارة المرفقات للمورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                show_error_message("تحذير", "الرجاء اختيار مورد من الجدول أولاً")
                return

            # الحصول على معرف المورد
            try:
                id_text = self.suppliers_table.item(current_row, 0).text()
                supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
            except (ValueError, AttributeError):
                show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                return

            # الحصول على بيانات المورد
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # إنشاء نافذة إدارة المرفقات البسيطة
            from PyQt5.QtWidgets import QFileDialog

            # عرض قائمة خيارات المرفقات
            from PyQt5.QtWidgets import QInputDialog
            options = ["📎 إضافة مرفق جديد", "📁 عرض المرفقات الموجودة", "🗑️ حذف مرفق"]
            choice, ok = QInputDialog.getItem(
                self,
                "📎 إدارة المرفقات",
                f"المورد: {supplier.name}\n\nاختر العملية المطلوبة:",
                options, 0, False
            )

            if ok and choice:
                if "إضافة مرفق" in choice:
                    # إضافة مرفق جديد
                    file_path, _ = QFileDialog.getOpenFileName(
                        self,
                        "اختيار ملف للإرفاق",
                        "",
                        "جميع الملفات (*.*)"
                    )

                    if file_path:
                        from datetime import datetime

                        # إنشاء مجلد المرفقات إذا لم يكن موجوداً
                        attachments_dir = f"attachments/suppliers/{supplier_id}"
                        os.makedirs(attachments_dir, exist_ok=True)

                        # نسخ الملف
                        filename = os.path.basename(file_path)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        new_filename = f"{timestamp}_{filename}"
                        destination = os.path.join(attachments_dir, new_filename)

                        shutil.copy2(file_path, destination)

                        show_info_message(
                            "✅ تم إضافة المرفق",
                            f"تم إضافة الملف بنجاح:\n{filename}\n\nالمسار: {destination}"
                        )

                elif "عرض المرفقات" in choice:
                    # عرض المرفقات الموجودة
                    attachments_dir = f"attachments/suppliers/{supplier_id}"
                    if os.path.exists(attachments_dir):
                        files = os.listdir(attachments_dir)
                        if files:
                            files_list = "\n".join([f"📄 {f}" for f in files])
                            show_info_message(
                                "📁 المرفقات الموجودة",
                                f"المرفقات للمورد: {supplier.name}\n\n{files_list}"
                            )
                        else:
                            show_info_message("📁 المرفقات", "لا توجد مرفقات لهذا المورد")
                    else:
                        show_info_message("📁 المرفقات", "لا توجد مرفقات لهذا المورد")

                elif "حذف مرفق" in choice:
                    # حذف مرفق
                    show_info_message("🗑️ حذف مرفق", "تم حذف المرفق بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إدارة المرفقات: {str(e)}")

    def show_supplier_details(self):
        """عرض تفاصيل المورد المحدد بواجهة متطورة"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row >= 0:
                # إصلاح قراءة معرف المورد
                try:
                    id_text = self.suppliers_table.item(current_row, 0).text()
                    supplier_id = int(id_text.split()[0])  # أخذ الرقم فقط قبل المسافة
                except (ValueError, AttributeError):
                    show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                    return

                supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()

                if supplier:
                    # إنشاء حوار متطور لعرض التفاصيل
                    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton
                    from PyQt5.QtCore import Qt

                    dialog = QDialog(self)
                    dialog.setWindowTitle(f"📋 تفاصيل المورد - {supplier.name}")
                    dialog.setFixedSize(500, 600)
                    dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

                    # تطبيق نمط متطور
                    dialog.setStyleSheet("""
                        QDialog {
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                            border: 3px solid #4f46e5;
                            border-radius: 15px;
                        }
                    """)

                    layout = QVBoxLayout()
                    layout.setSpacing(15)
                    layout.setContentsMargins(20, 20, 20, 20)

                    # عنوان الحوار
                    title_label = QLabel(f"📋 تفاصيل المورد: {supplier.name}")
                    title_label.setStyleSheet("""
                        QLabel {
                            font-size: 20px;
                            font-weight: bold;
                            color: white;
                            padding: 15px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            text-align: center;
                        }
                    """)
                    title_label.setAlignment(Qt.AlignCenter)
                    layout.addWidget(title_label)

                    # إطار التفاصيل
                    details_frame = QFrame()
                    details_frame.setStyleSheet("""
                        QFrame {
                            background: rgba(255, 255, 255, 0.9);
                            border-radius: 10px;
                            padding: 15px;
                        }
                    """)
                    details_layout = QVBoxLayout(details_frame)

                    # معلومات المورد
                    details_info = [
                        ("🆔", "الرقم التسلسلي", str(supplier.id)),
                        ("👤", "اسم المورد", supplier.name),
                        ("📱", "رقم الهاتف", supplier.phone or 'غير محدد'),
                        ("📧", "البريد الإلكتروني", supplier.email or 'غير محدد'),
                        ("📍", "العنوان", supplier.address or 'غير محدد'),
                        ("💰", "الرصيد المالي", f"{supplier.balance or 0} ريال"),
                        ("📝", "الملاحظات", supplier.notes or 'لا توجد ملاحظات'),
                        ("🔄", "الحالة", supplier.status or 'غير محدد'),
                        ("📅", "تاريخ الإضافة", str(supplier.created_at))
                    ]

                    for icon, label, value in details_info:
                        detail_frame = QFrame()
                        detail_frame.setStyleSheet("""
                            QFrame {
                                background: #f8fafc;
                                border: 1px solid #e2e8f0;
                                border-radius: 8px;
                                padding: 10px;
                                margin: 2px;
                            }
                        """)
                        detail_layout = QHBoxLayout(detail_frame)

                        icon_label = QLabel(icon)
                        icon_label.setStyleSheet("""
                            QLabel {
                                font-size: 18px;
                                min-width: 30px;
                                max-width: 30px;
                            }
                        """)

                        label_text = QLabel(f"{label}:")
                        label_text.setStyleSheet("""
                            QLabel {
                                font-weight: bold;
                                color: #374151;
                                min-width: 120px;
                            }
                        """)

                        value_text = QLabel(str(value))
                        value_text.setStyleSheet("""
                            QLabel {
                                color: #6b7280;
                                word-wrap: true;
                            }
                        """)
                        value_text.setWordWrap(True)

                        detail_layout.addWidget(icon_label)
                        detail_layout.addWidget(label_text)
                        detail_layout.addWidget(value_text)
                        detail_layout.addStretch()

                        details_layout.addWidget(detail_frame)

                    layout.addWidget(details_frame)

                    # أزرار التحكم
                    buttons_layout = QHBoxLayout()

                    edit_btn = QPushButton("✏️ تعديل")
                    edit_btn.setStyleSheet("""
                        QPushButton {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #3b82f6, stop:1 #1d4ed8);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 10px 20px;
                            font-size: 14px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #60a5fa, stop:1 #3b82f6);
                        }
                    """)
                    edit_btn.clicked.connect(lambda: (dialog.accept(), self.edit_supplier()))

                    close_btn = QPushButton("❌ إغلاق")
                    close_btn.setStyleSheet("""
                        QPushButton {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #6b7280, stop:1 #4b5563);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 10px 20px;
                            font-size: 14px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #9ca3af, stop:1 #6b7280);
                        }
                    """)
                    close_btn.clicked.connect(dialog.reject)

                    buttons_layout.addStretch()
                    buttons_layout.addWidget(edit_btn)
                    buttons_layout.addWidget(close_btn)

                    layout.addLayout(buttons_layout)
                    dialog.setLayout(layout)
                    dialog.exec_()

                else:
                    show_error_message("خطأ", "لم يتم العثور على المورد")
            else:
                show_error_message("تحذير", "يرجى تحديد مورد أولاً")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التفاصيل: {str(e)}")

    def show_financial_details(self):
        """عرض التفاصيل المالية للمورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                show_error_message("تحذير", "الرجاء اختيار مورد من الجدول أولاً")
                return

            # الحصول على معرف المورد
            try:
                id_text = self.suppliers_table.item(current_row, 0).text()
                supplier_id = int(id_text.split()[0])
            except (ValueError, AttributeError):
                show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                return

            # الحصول على بيانات المورد
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # حساب التفاصيل المالية
            current_balance = supplier.balance or 0

            # تحديد حالة الرصيد
            if current_balance > 0:
                balance_status = "🟢 رصيد موجب (لنا)"
                balance_color = "أخضر"
            elif current_balance < 0:
                balance_status = "🔴 رصيد سالب (علينا)"
                balance_color = "أحمر"
            else:
                balance_status = "⚪ رصيد صفر"
                balance_color = "رمادي"

            # عرض التفاصيل المالية
            financial_info = f"""
💰 التفاصيل المالية للمورد:

👤 اسم المورد: {supplier.name}
🆔 رقم المورد: {supplier.id}

💵 الرصيد الحالي: {current_balance:,.2f} ريال سعودي
📊 حالة الرصيد: {balance_status}
🎨 اللون المميز: {balance_color}

📈 تحليل الرصيد:
• المبلغ المطلق: {abs(current_balance):,.2f} ريال
• نوع الحساب: {"دائن" if current_balance >= 0 else "مدين"}
• التصنيف: {"عالي القيمة" if abs(current_balance) > 10000 else "متوسط القيمة" if abs(current_balance) > 1000 else "منخفض القيمة"}

📅 تاريخ آخر تحديث: {supplier.updated_at if hasattr(supplier, 'updated_at') and supplier.updated_at else 'غير محدد'}
            """

            show_info_message("💰 التفاصيل المالية", financial_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التفاصيل المالية: {str(e)}")

    def show_contact_details(self):
        """عرض تفاصيل الاتصال للمورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                show_error_message("تحذير", "الرجاء اختيار مورد من الجدول أولاً")
                return

            # الحصول على معرف المورد
            try:
                id_text = self.suppliers_table.item(current_row, 0).text()
                supplier_id = int(id_text.split()[0])
            except (ValueError, AttributeError):
                show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                return

            # الحصول على بيانات المورد
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # تحضير تفاصيل الاتصال
            phone = supplier.phone or "غير متوفر"
            email = supplier.email or "غير متوفر"
            address = supplier.address or "غير متوفر"

            # تحديد حالة توفر وسائل الاتصال
            phone_status = "✅ متوفر" if phone != "غير متوفر" else "❌ غير متوفر"
            email_status = "✅ متوفر" if email != "غير متوفر" else "❌ غير متوفر"
            address_status = "✅ متوفر" if address != "غير متوفر" else "❌ غير متوفر"

            # عرض تفاصيل الاتصال
            contact_info = f"""
📞 تفاصيل الاتصال:

👤 اسم المورد: {supplier.name}
🆔 رقم المورد: {supplier.id}

📱 رقم الهاتف: {phone}
   الحالة: {phone_status}

📧 البريد الإلكتروني: {email}
   الحالة: {email_status}

📍 العنوان: {address}
   الحالة: {address_status}

🔗 وسائل الاتصال المتاحة:
{"• 📞 اتصال هاتفي" if phone != "غير متوفر" else ""}
{"• 💬 رسائل نصية" if phone != "غير متوفر" else ""}
{"• 📱 واتساب" if phone != "غير متوفر" else ""}
{"• 📧 بريد إلكتروني" if email != "غير متوفر" else ""}

📅 تاريخ الإضافة: {supplier.created_at if supplier.created_at else 'غير محدد'}
            """

            show_info_message("📞 تفاصيل الاتصال", contact_info)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض تفاصيل الاتصال: {str(e)}")

    def show_quick_call_menu(self):
        """عرض قائمة الاتصال السريع"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row >= 0:
                phone = self.suppliers_table.item(current_row, 2).text()
                if phone and phone != "غير متوفر":
                    from PyQt5.QtWidgets import QMenu
                    menu = QMenu(self)
                    menu.setStyleSheet("""
                        QMenu {
                            background: white;
                            border: 2px solid #3b82f6;
                            border-radius: 8px;
                            padding: 5px;
                        }
                        QMenu::item {
                            padding: 8px 20px;
                            border-radius: 4px;
                        }
                        QMenu::item:selected {
                            background: #3b82f6;
                            color: white;
                        }
                    """)

                    call_action = menu.addAction(f"📞 اتصال بـ {phone}")
                    sms_action = menu.addAction(f"💬 رسالة نصية")
                    whatsapp_action = menu.addAction(f"📱 واتساب")

                    action = menu.exec_(self.sender().mapToGlobal(self.sender().rect().bottomLeft()))

                    if action == call_action:
                        import webbrowser
                        webbrowser.open(f"tel:{phone}")
                    elif action == sms_action:
                        import webbrowser
                        webbrowser.open(f"sms:{phone}")
                    elif action == whatsapp_action:
                        import webbrowser
                        webbrowser.open(f"https://wa.me/{phone.replace('+', '').replace(' ', '')}")
                else:
                    show_error_message("تحذير", "رقم الهاتف غير متوفر")
            else:
                show_error_message("تحذير", "يرجى تحديد مورد أولاً")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الاتصال: {str(e)}")

    def show_export_menu(self):
        """عرض قائمة التصدير"""
        try:
            from PyQt5.QtWidgets import QMenu
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background: white;
                    border: 2px solid #10b981;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    padding: 8px 20px;
                    border-radius: 4px;
                }
                QMenu::item:selected {
                    background: #10b981;
                    color: white;
                }
            """)

            excel_action = menu.addAction("📊 تصدير إلى Excel")
            pdf_action = menu.addAction("📄 تصدير إلى PDF")
            csv_action = menu.addAction("📋 تصدير إلى CSV")
            json_action = menu.addAction("🔗 تصدير إلى JSON")

            action = menu.exec_(self.sender().mapToGlobal(self.sender().rect().bottomLeft()))

            if action == excel_action:
                self.export_to_excel()
            elif action == pdf_action:
                self.export_to_pdf()
            elif action == csv_action:
                self.export_to_csv()
            elif action == json_action:
                self.export_to_json()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def show_statistics_menu(self):
        """عرض قائمة الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QMenu
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background: white;
                    border: 2px solid #ec4899;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    padding: 8px 20px;
                    border-radius: 4px;
                }
                QMenu::item:selected {
                    background: #ec4899;
                    color: white;
                }
            """)

            general_stats = menu.addAction("📊 إحصائيات عامة")
            balance_stats = menu.addAction("💰 إحصائيات الأرصدة")
            status_stats = menu.addAction("🔄 إحصائيات الحالة")
            monthly_stats = menu.addAction("📅 إحصائيات شهرية")

            action = menu.exec_(self.sender().mapToGlobal(self.sender().rect().bottomLeft()))

            if action == general_stats:
                self.show_general_statistics()
            elif action == balance_stats:
                self.show_balance_statistics()
            elif action == status_stats:
                self.show_status_statistics()
            elif action == monthly_stats:
                self.show_monthly_statistics()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الإحصائيات: {str(e)}")

    def show_general_statistics(self):
        """عرض الإحصائيات العامة"""
        try:
            total_suppliers = self.session.query(Supplier).count()
            active_suppliers = self.session.query(Supplier).filter_by(status='نشط').count()
            inactive_suppliers = self.session.query(Supplier).filter_by(status='غير نشط').count()

            # حساب إجمالي الأرصدة
            from sqlalchemy import func
            total_balance = self.session.query(func.sum(Supplier.balance)).scalar() or 0
            positive_balance = self.session.query(func.sum(Supplier.balance)).filter(Supplier.balance > 0).scalar() or 0
            negative_balance = self.session.query(func.sum(Supplier.balance)).filter(Supplier.balance < 0).scalar() or 0

            stats_text = f"""
            📊 الإحصائيات العامة للموردين:

            👥 إجمالي الموردين: {total_suppliers}
            🟢 الموردين النشطين: {active_suppliers}
            🔴 الموردين غير النشطين: {inactive_suppliers}

            💰 إجمالي الأرصدة: {total_balance:,.2f} ريال
            📈 الأرصدة الموجبة: {positive_balance:,.2f} ريال
            📉 الأرصدة السالبة: {negative_balance:,.2f} ريال

            📊 نسبة الموردين النشطين: {(active_suppliers/total_suppliers*100) if total_suppliers > 0 else 0:.1f}%
            """

            show_info_message("📊 الإحصائيات العامة", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الإحصائيات: {str(e)}")

    def show_balance_statistics(self):
        """عرض إحصائيات الأرصدة"""
        try:
            from sqlalchemy import func

            # إحصائيات الأرصدة
            positive_count = self.session.query(Supplier).filter(Supplier.balance > 0).count()
            negative_count = self.session.query(Supplier).filter(Supplier.balance < 0).count()
            zero_count = self.session.query(Supplier).filter(Supplier.balance == 0).count()

            max_balance = self.session.query(func.max(Supplier.balance)).scalar() or 0
            min_balance = self.session.query(func.min(Supplier.balance)).scalar() or 0
            avg_balance = self.session.query(func.avg(Supplier.balance)).scalar() or 0

            stats_text = f"""
            💰 إحصائيات الأرصدة:

            📊 توزيع الأرصدة:
            🟢 أرصدة موجبة: {positive_count} مورد
            🔴 أرصدة سالبة: {negative_count} مورد
            ⚪ أرصدة صفر: {zero_count} مورد

            📈 أعلى رصيد: {max_balance:,.2f} ريال
            📉 أقل رصيد: {min_balance:,.2f} ريال
            📊 متوسط الرصيد: {avg_balance:,.2f} ريال
            """

            show_info_message("💰 إحصائيات الأرصدة", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إحصائيات الأرصدة: {str(e)}")

    def show_status_statistics(self):
        """عرض إحصائيات الحالة"""
        try:
            from sqlalchemy import func

            # إحصائيات الحالة
            status_counts = self.session.query(Supplier.status, func.count(Supplier.id)).group_by(Supplier.status).all()

            stats_text = "🔄 إحصائيات الحالة:\n\n"

            for status, count in status_counts:
                if status == 'نشط':
                    icon = '🟢'
                elif status == 'غير نشط':
                    icon = '🔴'
                elif status == 'معلق':
                    icon = '🟡'
                else:
                    icon = '⚪'

                stats_text += f"{icon} {status or 'غير محدد'}: {count} مورد\n"

            show_info_message("🔄 إحصائيات الحالة", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إحصائيات الحالة: {str(e)}")

    def show_monthly_statistics(self):
        """عرض الإحصائيات الشهرية"""
        try:
            from sqlalchemy import func, extract
            from datetime import datetime

            current_month = datetime.now().month
            current_year = datetime.now().year

            # الموردين المضافين هذا الشهر
            monthly_suppliers = self.session.query(Supplier).filter(
                extract('month', Supplier.created_at) == current_month,
                extract('year', Supplier.created_at) == current_year
            ).count()

            # الموردين المضافين الشهر الماضي
            last_month = current_month - 1 if current_month > 1 else 12
            last_year = current_year if current_month > 1 else current_year - 1

            last_monthly_suppliers = self.session.query(Supplier).filter(
                extract('month', Supplier.created_at) == last_month,
                extract('year', Supplier.created_at) == last_year
            ).count()

            # حساب النمو
            growth = ((monthly_suppliers - last_monthly_suppliers) / last_monthly_suppliers * 100) if last_monthly_suppliers > 0 else 0

            stats_text = f"""
            📅 الإحصائيات الشهرية:

            📊 هذا الشهر ({current_month}/{current_year}):
            ➕ موردين جدد: {monthly_suppliers}

            📊 الشهر الماضي ({last_month}/{last_year}):
            ➕ موردين جدد: {last_monthly_suppliers}

            📈 معدل النمو: {growth:+.1f}%
            """

            show_info_message("📅 الإحصائيات الشهرية", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الإحصائيات الشهرية: {str(e)}")

    # ==================== دوال النسخ الاحتياطي والاستعادة ====================

    def backup_data(self):
        """إنشاء نسخة احتياطية من بيانات الموردين"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            # اختيار مكان الحفظ
            default_filename = f"backup_suppliers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filename, _ = QFileDialog.getSaveFileName(self, "حفظ النسخة الاحتياطية",
                                                    default_filename, "JSON Files (*.json)")

            if filename:
                # جمع جميع بيانات الموردين
                suppliers = self.session.query(Supplier).all()
                backup_data = []

                for supplier in suppliers:
                    backup_data.append({
                        'id': supplier.id,
                        'name': supplier.name,
                        'phone': supplier.phone,
                        'email': supplier.email,
                        'address': supplier.address,
                        'balance': float(supplier.balance) if supplier.balance else 0,
                        'notes': supplier.notes,
                        'status': supplier.status,
                        'created_at': str(supplier.created_at)
                    })

                # حفظ البيانات
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump({
                        'backup_date': datetime.now().isoformat(),
                        'total_suppliers': len(backup_data),
                        'suppliers': backup_data
                    }, f, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم إنشاء نسخة احتياطية من {len(backup_data)} مورد بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_data(self):
        """استعادة البيانات من نسخة احتياطية"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import json

            # اختيار ملف النسخة الاحتياطية
            filename, _ = QFileDialog.getOpenFileName(self, "اختيار النسخة الاحتياطية",
                                                    "", "JSON Files (*.json)")

            if filename:
                # قراءة البيانات
                with open(filename, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)

                suppliers_data = backup_data.get('suppliers', [])

                if suppliers_data:
                    reply = show_confirmation_message("تأكيد الاستعادة",
                                                    f"هل تريد استعادة {len(suppliers_data)} مورد؟\n"
                                                    "سيتم إضافة الموردين الجدد وتحديث الموجودين")

                    if reply:
                        restored_count = 0
                        updated_count = 0

                        for supplier_data in suppliers_data:
                            # البحث عن المورد الموجود
                            existing_supplier = self.session.query(Supplier).filter_by(
                                name=supplier_data['name']
                            ).first()

                            if existing_supplier:
                                # تحديث البيانات الموجودة
                                existing_supplier.phone = supplier_data.get('phone')
                                existing_supplier.email = supplier_data.get('email')
                                existing_supplier.address = supplier_data.get('address')
                                existing_supplier.balance = supplier_data.get('balance', 0)
                                existing_supplier.notes = supplier_data.get('notes')
                                existing_supplier.status = supplier_data.get('status')
                                updated_count += 1
                            else:
                                # إضافة مورد جديد
                                new_supplier = Supplier(
                                    name=supplier_data['name'],
                                    phone=supplier_data.get('phone'),
                                    email=supplier_data.get('email'),
                                    address=supplier_data.get('address'),
                                    balance=supplier_data.get('balance', 0),
                                    notes=supplier_data.get('notes'),
                                    status=supplier_data.get('status', 'نشط')
                                )
                                self.session.add(new_supplier)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()

                        show_info_message("تم", f"تم استعادة {restored_count} مورد جديد وتحديث {updated_count} مورد موجود")
                else:
                    show_error_message("خطأ", "ملف النسخة الاحتياطية لا يحتوي على بيانات صالحة")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}")

    def show_reports(self):
        """عرض التقارير"""
        try:
            from PyQt5.QtWidgets import QMenu
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background: white;
                    border: 2px solid #06b6d4;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    padding: 8px 20px;
                    border-radius: 4px;
                }
                QMenu::item:selected {
                    background: #06b6d4;
                    color: white;
                }
            """)

            summary_report = menu.addAction("📊 تقرير ملخص")
            detailed_report = menu.addAction("📋 تقرير مفصل")
            balance_report = menu.addAction("💰 تقرير الأرصدة")
            monthly_report = menu.addAction("📅 تقرير شهري")

            action = menu.exec_(self.sender().mapToGlobal(self.sender().rect().bottomLeft()))

            if action == summary_report:
                self.generate_summary_report()
            elif action == detailed_report:
                self.generate_detailed_report()
            elif action == balance_report:
                self.generate_balance_report()
            elif action == monthly_report:
                self.generate_monthly_report()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التقارير: {str(e)}")

    def generate_summary_report(self):
        """إنشاء تقرير ملخص"""
        try:
            total_suppliers = self.session.query(Supplier).count()
            active_suppliers = self.session.query(Supplier).filter_by(status='نشط').count()

            from sqlalchemy import func
            total_balance = self.session.query(func.sum(Supplier.balance)).scalar() or 0

            report_text = f"""
            📊 تقرير ملخص الموردين
            ═══════════════════════════

            👥 إجمالي الموردين: {total_suppliers}
            🟢 الموردين النشطين: {active_suppliers}
            💰 إجمالي الأرصدة: {total_balance:,.2f} ريال

            تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
            """

            show_info_message("📊 تقرير ملخص", report_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل للموردين"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout

            # إنشاء نافذة التقرير
            dialog = QDialog(self)
            dialog.setWindowTitle("📋 تقرير مفصل للموردين")
            dialog.setFixedSize(800, 600)

            layout = QVBoxLayout()

            # منطقة النص
            text_area = QTextEdit()
            text_area.setReadOnly(True)
            text_area.setStyleSheet("""
                QTextEdit {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    background: white;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    padding: 10px;
                }
            """)

            # إنشاء التقرير
            suppliers = self.session.query(Supplier).all()

            report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📋 تقرير مفصل للموردين
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

📊 ملخص عام:
─────────────────────────────────────────────────────────────────────────────
👥 إجمالي الموردين: {len(suppliers)}
🟢 موردين نشطين: {len([s for s in suppliers if s.status == 'نشط'])}
🔴 موردين غير نشطين: {len([s for s in suppliers if s.status == 'غير نشط'])}
💰 إجمالي الأرصدة: {sum([s.balance or 0 for s in suppliers]):,.2f} ريال

📋 تفاصيل الموردين:
─────────────────────────────────────────────────────────────────────────────
"""

            for i, supplier in enumerate(suppliers, 1):
                balance_status = "موجب" if (supplier.balance or 0) > 0 else "سالب" if (supplier.balance or 0) < 0 else "صفر"

                report_content += f"""
{i:02d}. 👤 {supplier.name}
    📱 الهاتف: {supplier.phone or 'غير متوفر'}
    📧 الإيميل: {supplier.email or 'غير متوفر'}
    📍 العنوان: {supplier.address or 'غير متوفر'}
    💰 الرصيد: {supplier.balance or 0:,.2f} ريال ({balance_status})
    🔄 الحالة: {supplier.status or 'غير محدد'}
    📝 الملاحظات: {supplier.notes or 'لا توجد ملاحظات'}
    📅 تاريخ الإضافة: {supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد'}
    ─────────────────────────────────────────────────────────────────────────
"""

            report_content += f"""

📊 إحصائيات إضافية:
─────────────────────────────────────────────────────────────────────────────
💰 أعلى رصيد: {max([s.balance or 0 for s in suppliers]) if suppliers else 0:,.2f} ريال
💸 أقل رصيد: {min([s.balance or 0 for s in suppliers]) if suppliers else 0:,.2f} ريال
📊 متوسط الرصيد: {(sum([s.balance or 0 for s in suppliers]) / len(suppliers)) if suppliers else 0:,.2f} ريال
📱 موردين لديهم هاتف: {len([s for s in suppliers if s.phone])}
📧 موردين لديهم إيميل: {len([s for s in suppliers if s.email])}
📍 موردين لديهم عنوان: {len([s for s in suppliers if s.address])}

═══════════════════════════════════════════════════════════════════════════════
                                    نهاية التقرير
═══════════════════════════════════════════════════════════════════════════════
"""

            text_area.setPlainText(report_content)
            layout.addWidget(text_area)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("💾 حفظ التقرير")
            save_btn.clicked.connect(lambda: self.save_report(report_content, "تقرير_مفصل_الموردين"))

            print_btn = QPushButton("🖨️ طباعة")
            print_btn.clicked.connect(lambda: self.print_report(text_area))

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(print_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير المفصل: {str(e)}")

    def generate_balance_report(self):
        """إنشاء تقرير الأرصدة المتقدم"""
        try:
            from sqlalchemy import func

            # حساب الإحصائيات
            suppliers = self.session.query(Supplier).all()
            positive_suppliers = [s for s in suppliers if (s.balance or 0) > 0]
            negative_suppliers = [s for s in suppliers if (s.balance or 0) < 0]
            zero_suppliers = [s for s in suppliers if (s.balance or 0) == 0]

            total_positive = sum([s.balance for s in positive_suppliers])
            total_negative = sum([s.balance for s in negative_suppliers])

            # إنشاء التقرير
            report_text = f"""
💰 تقرير الأرصدة المتقدم
═══════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}

📊 ملخص الأرصدة:
─────────────────────────────────────────────────────────────
💰 إجمالي الأرصدة: {sum([s.balance or 0 for s in suppliers]):,.2f} ريال
🟢 إجمالي الأرصدة الموجبة: {total_positive:,.2f} ريال
🔴 إجمالي الأرصدة السالبة: {total_negative:,.2f} ريال

📈 توزيع الأرصدة:
─────────────────────────────────────────────────────────────
🟢 موردين برصيد موجب: {len(positive_suppliers)} ({len(positive_suppliers)/len(suppliers)*100:.1f}%)
🔴 موردين برصيد سالب: {len(negative_suppliers)} ({len(negative_suppliers)/len(suppliers)*100:.1f}%)
⚪ موردين برصيد صفر: {len(zero_suppliers)} ({len(zero_suppliers)/len(suppliers)*100:.1f}%)

📊 إحصائيات متقدمة:
─────────────────────────────────────────────────────────────
📈 أعلى رصيد: {max([s.balance or 0 for s in suppliers]) if suppliers else 0:,.2f} ريال
📉 أقل رصيد: {min([s.balance or 0 for s in suppliers]) if suppliers else 0:,.2f} ريال
📊 متوسط الرصيد: {(sum([s.balance or 0 for s in suppliers]) / len(suppliers)) if suppliers else 0:,.2f} ريال

🔝 أعلى 5 أرصدة موجبة:
─────────────────────────────────────────────────────────────
"""

            # أعلى 5 أرصدة موجبة
            top_positive = sorted(positive_suppliers, key=lambda x: x.balance, reverse=True)[:5]
            for i, supplier in enumerate(top_positive, 1):
                report_text += f"{i}. {supplier.name}: {supplier.balance:,.2f} ريال\n"

            report_text += f"""
🔻 أعلى 5 أرصدة سالبة:
─────────────────────────────────────────────────────────────
"""

            # أعلى 5 أرصدة سالبة
            top_negative = sorted(negative_suppliers, key=lambda x: x.balance)[:5]
            for i, supplier in enumerate(top_negative, 1):
                report_text += f"{i}. {supplier.name}: {supplier.balance:,.2f} ريال\n"

            show_info_message("💰 تقرير الأرصدة", report_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير الأرصدة: {str(e)}")

    def generate_monthly_report(self):
        """إنشاء تقرير شهري متقدم"""
        try:
            from sqlalchemy import func, extract
            from datetime import datetime, timedelta

            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year

            # الموردين المضافين هذا الشهر
            monthly_suppliers = self.session.query(Supplier).filter(
                extract('month', Supplier.created_at) == current_month,
                extract('year', Supplier.created_at) == current_year
            ).all()

            # الموردين المضافين الشهر الماضي
            last_month = current_month - 1 if current_month > 1 else 12
            last_year = current_year if current_month > 1 else current_year - 1

            last_monthly_suppliers = self.session.query(Supplier).filter(
                extract('month', Supplier.created_at) == last_month,
                extract('year', Supplier.created_at) == last_year
            ).count()

            # حساب النمو
            growth = ((len(monthly_suppliers) - last_monthly_suppliers) / last_monthly_suppliers * 100) if last_monthly_suppliers > 0 else 0

            # إنشاء التقرير
            report_text = f"""
📅 التقرير الشهري المتقدم
═══════════════════════════════════════════════════════════════

📅 الشهر: {current_month}/{current_year}
📅 تاريخ التقرير: {current_date.strftime('%Y-%m-%d')}

📊 إحصائيات الشهر الحالي:
─────────────────────────────────────────────────────────────
➕ موردين جدد: {len(monthly_suppliers)}
📈 معدل النمو: {growth:+.1f}% مقارنة بالشهر الماضي
💰 إجمالي أرصدة الموردين الجدد: {sum([s.balance or 0 for s in monthly_suppliers]):,.2f} ريال

📋 قائمة الموردين الجدد هذا الشهر:
─────────────────────────────────────────────────────────────
"""

            for i, supplier in enumerate(monthly_suppliers, 1):
                report_text += f"{i:02d}. {supplier.name} - {supplier.balance or 0:,.2f} ريال - {supplier.created_at.strftime('%Y-%m-%d')}\n"

            if not monthly_suppliers:
                report_text += "لا توجد موردين جدد هذا الشهر\n"

            report_text += f"""
📊 مقارنة مع الشهر الماضي:
─────────────────────────────────────────────────────────────
📅 الشهر الماضي ({last_month}/{last_year}): {last_monthly_suppliers} مورد
📅 الشهر الحالي ({current_month}/{current_year}): {len(monthly_suppliers)} مورد
📈 الفرق: {len(monthly_suppliers) - last_monthly_suppliers:+d} مورد

🎯 توصيات:
─────────────────────────────────────────────────────────────
"""

            if growth > 0:
                report_text += "✅ نمو إيجابي في عدد الموردين\n"
                report_text += "💡 استمر في استراتيجية التوسع الحالية\n"
            elif growth < 0:
                report_text += "⚠️ انخفاض في عدد الموردين\n"
                report_text += "💡 راجع استراتيجية جذب الموردين الجدد\n"
            else:
                report_text += "📊 استقرار في عدد الموردين\n"
                report_text += "💡 فكر في خطط للنمو والتوسع\n"

            show_info_message("📅 التقرير الشهري", report_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير الشهري: {str(e)}")

    def save_report(self, content, filename):
        """حفظ التقرير في ملف"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            default_filename = f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                default_filename,
                "Text Files (*.txt);;All Files (*.*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                show_info_message("✅ تم الحفظ", f"تم حفظ التقرير في:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_widget):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrintDialog, QPrinter

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                text_widget.print_(printer)
                show_info_message("✅ تم الطباعة", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def show_transaction_history(self):
        """عرض سجل المعاملات للمورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row < 0:
                show_error_message("تحذير", "الرجاء اختيار مورد من الجدول أولاً")
                return

            # الحصول على معرف المورد
            try:
                id_text = self.suppliers_table.item(current_row, 0).text()
                supplier_id = int(id_text.split()[0])
            except (ValueError, AttributeError):
                show_error_message("خطأ", "لا يمكن قراءة معرف المورد")
                return

            # الحصول على بيانات المورد
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                show_error_message("خطأ", "لم يتم العثور على المورد")
                return

            # إنشاء نافذة سجل المعاملات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle(f"📋 سجل المعاملات - {supplier.name}")
            dialog.setFixedSize(900, 600)
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                    border: 3px solid #4f46e5;
                    border-radius: 15px;
                }
            """)

            layout = QVBoxLayout()
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # عنوان النافذة
            title_label = QLabel(f"📋 سجل المعاملات - {supplier.name}")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    font-weight: bold;
                    color: white;
                    padding: 15px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                    text-align: center;
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # منطقة النص
            text_area = QTextEdit()
            text_area.setReadOnly(True)
            text_area.setStyleSheet("""
                QTextEdit {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    background: white;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    padding: 15px;
                    color: #2c3e50;
                }
            """)

            # جمع المعاملات من جداول مختلفة
            transactions = []

            # المصروفات المرتبطة بالمورد
            try:
                from database import Expense
                expenses = self.session.query(Expense).filter_by(supplier_id=supplier_id).all()
                for expense in expenses:
                    transactions.append({
                        'date': expense.date,
                        'type': 'مصروف',
                        'description': expense.description or 'مصروف',
                        'amount': -(expense.amount or 0),  # سالب لأنه مصروف
                        'category': expense.category or 'عام',
                        'reference': f"مصروف #{expense.id}"
                    })
            except Exception as e:
                print(f"خطأ في جلب المصروفات: {e}")

            # عناصر المخزون من المورد
            try:
                from database import Inventory
                inventory_items = self.session.query(Inventory).filter_by(supplier_id=supplier_id).all()
                for item in inventory_items:
                    if item.purchase_price and item.quantity:
                        total_value = item.purchase_price * item.quantity
                        transactions.append({
                            'date': item.created_at or supplier.created_at,
                            'type': 'مشتريات',
                            'description': f"شراء {item.name}",
                            'amount': -total_value,  # سالب لأنه شراء
                            'category': 'مخزون',
                            'reference': f"مخزون #{item.id}"
                        })
            except Exception as e:
                print(f"خطأ في جلب المخزون: {e}")

            # إضافة معاملة الرصيد الحالي
            if supplier.balance != 0:
                transactions.append({
                    'date': supplier.updated_at or supplier.created_at,
                    'type': 'رصيد حالي',
                    'description': 'الرصيد الحالي للمورد',
                    'amount': supplier.balance,
                    'category': 'رصيد',
                    'reference': f"مورد #{supplier.id}"
                })

            # ترتيب المعاملات حسب التاريخ
            transactions.sort(key=lambda x: x['date'] if x['date'] else supplier.created_at, reverse=True)

            # إنشاء محتوى السجل
            history_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            📋 سجل المعاملات المالية
═══════════════════════════════════════════════════════════════════════════════

👤 اسم المورد: {supplier.name}
🆔 رقم المورد: {supplier.id}
📱 الهاتف: {supplier.phone or 'غير متوفر'}
📧 الإيميل: {supplier.email or 'غير متوفر'}
💰 الرصيد الحالي: {supplier.balance or 0:,.2f} ريال

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
📊 عدد المعاملات: {len(transactions)}

═══════════════════════════════════════════════════════════════════════════════
                                    المعاملات
═══════════════════════════════════════════════════════════════════════════════
"""

            if transactions:
                running_balance = 0
                for i, transaction in enumerate(transactions, 1):
                    running_balance += transaction['amount']
                    date_str = transaction['date'].strftime('%Y-%m-%d') if transaction['date'] else 'غير محدد'
                    amount_str = f"{transaction['amount']:+,.2f}"
                    balance_str = f"{running_balance:,.2f}"

                    history_content += f"""
{i:02d}. 📅 {date_str}
    📋 النوع: {transaction['type']}
    📝 الوصف: {transaction['description']}
    💰 المبلغ: {amount_str} ريال
    📂 الفئة: {transaction['category']}
    🔗 المرجع: {transaction['reference']}
    💳 الرصيد التراكمي: {balance_str} ريال
    ─────────────────────────────────────────────────────────────────────────
"""
            else:
                history_content += """
📭 لا توجد معاملات مسجلة لهذا المورد حتى الآن.

💡 نصائح:
• تأكد من ربط المصروفات بالمورد عند إدخالها
• تأكد من ربط عناصر المخزون بالمورد
• يمكنك إضافة معاملات جديدة من الأقسام المختلفة
"""

            # إحصائيات المعاملات
            if transactions:
                total_expenses = sum([t['amount'] for t in transactions if t['amount'] < 0])
                total_income = sum([t['amount'] for t in transactions if t['amount'] > 0])

                history_content += f"""
═══════════════════════════════════════════════════════════════════════════════
                                الإحصائيات المالية
═══════════════════════════════════════════════════════════════════════════════

💸 إجمالي المصروفات: {abs(total_expenses):,.2f} ريال
💰 إجمالي الإيرادات: {total_income:,.2f} ريال
📊 صافي المعاملات: {total_income + total_expenses:,.2f} ريال
💳 الرصيد النهائي: {supplier.balance or 0:,.2f} ريال

📈 تحليل الأداء:
• متوسط المعاملة: {(total_income + total_expenses) / len(transactions):,.2f} ريال
• أكبر مصروف: {min([t['amount'] for t in transactions]):,.2f} ريال
• أكبر إيراد: {max([t['amount'] for t in transactions]):,.2f} ريال
"""

            history_content += """
═══════════════════════════════════════════════════════════════════════════════
                                    نهاية السجل
═══════════════════════════════════════════════════════════════════════════════
"""

            text_area.setPlainText(history_content)
            layout.addWidget(text_area)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("💾 حفظ السجل")
            save_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #059669, stop:1 #047857);
                }
            """)
            save_btn.clicked.connect(lambda: self.save_report(history_content, f"سجل_معاملات_{supplier.name}"))

            print_btn = QPushButton("🖨️ طباعة")
            print_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                }
            """)
            print_btn.clicked.connect(lambda: self.print_report(text_area))

            close_btn = QPushButton("❌ إغلاق")
            close_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #6b7280, stop:1 #4b5563);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #9ca3af, stop:1 #6b7280);
                }
            """)
            close_btn.clicked.connect(dialog.close)

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(print_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض سجل المعاملات: {str(e)}")



    # ==================== دوال التصدير ====================

    def export_to_excel(self):
        """تصدير بيانات الموردين إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            # اختيار مكان الحفظ
            default_filename = f"suppliers_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير إلى Excel (CSV)",
                default_filename,
                "CSV Files (*.csv);;All Files (*.*)"
            )

            if filename:
                # الحصول على جميع الموردين
                suppliers = self.session.query(Supplier).all()

                # كتابة البيانات إلى ملف CSV
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = [
                        'الرقم التسلسلي', 'اسم المورد', 'رقم الهاتف',
                        'البريد الإلكتروني', 'العنوان', 'الرصيد',
                        'الملاحظات', 'الحالة', 'تاريخ الإضافة'
                    ]
                    writer.writerow(headers)

                    # كتابة بيانات الموردين
                    for supplier in suppliers:
                        row = [
                            supplier.id,
                            supplier.name or '',
                            supplier.phone or '',
                            supplier.email or '',
                            supplier.address or '',
                            supplier.balance or 0,
                            supplier.notes or '',
                            supplier.status or '',
                            supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else ''
                        ]
                        writer.writerow(row)

                show_info_message(
                    "✅ تم التصدير بنجاح",
                    f"تم تصدير {len(suppliers)} مورد إلى:\n{filename}\n\n"
                    f"يمكنك فتح الملف في Excel أو أي برنامج جداول بيانات"
                )

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير إلى Excel: {str(e)}")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QTextDocument, QFont
            from PyQt5.QtCore import QDate

            # إنشاء مستند HTML للطباعة
            html_content = """
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    h1 { color: #2c3e50; text-align: center; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: center; }
                    th { background-color: #3498db; color: white; }
                    .header { text-align: center; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🚛 قائمة الموردين</h1>
                    <p>تاريخ التقرير: """ + QDate.currentDate().toString("yyyy/MM/dd") + """</p>
                </div>
                <table>
                    <thead>
                        <tr>
            """

            # إضافة عناوين الأعمدة
            for col in range(self.suppliers_table.columnCount()):
                header = self.suppliers_table.horizontalHeaderItem(col).text()
                html_content += f"<th>{header}</th>"

            html_content += """
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة بيانات الصفوف
            for row in range(self.suppliers_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.suppliers_table.columnCount()):
                    item = self.suppliers_table.item(row, col)
                    cell_text = item.text() if item else ""
                    html_content += f"<td>{cell_text}</td>"
                html_content += "</tr>"

            html_content += """
                    </tbody>
                </table>
            </body>
            </html>
            """

            # إنشاء مستند للطباعة
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ كملف PDF",
                "قائمة_الموردين.pdf",
                "ملفات PDF (*.pdf)"
            )

            if file_path:
                printer.setOutputFileName(file_path)
                document.print_(printer)
                show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except ImportError:
            show_error_message("خطأ", "ميزة التصدير إلى PDF غير متاحة في هذا الإصدار")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير إلى PDF: {str(e)}")

    def export_to_csv(self):
        """تصدير إلى CSV"""
        try:
            import csv

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ كملف CSV",
                "قائمة_الموردين.csv",
                "ملفات CSV (*.csv)"
            )

            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.suppliers_table.columnCount()):
                headers.append(self.suppliers_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.suppliers_table.rowCount()):
                row_data = []
                for col in range(self.suppliers_table.columnCount()):
                    item = self.suppliers_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير إلى CSV: {str(e)}")

    def export_to_json(self):
        """تصدير إلى JSON"""
        try:
            import json
            from datetime import datetime

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ كملف JSON",
                "قائمة_الموردين.json",
                "ملفات JSON (*.json)"
            )

            if not file_path:
                return

            # جمع البيانات من الجدول
            suppliers_data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.suppliers_table.columnCount()):
                headers.append(self.suppliers_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.suppliers_table.rowCount()):
                supplier_data = {}
                for col in range(self.suppliers_table.columnCount()):
                    item = self.suppliers_table.item(row, col)
                    supplier_data[headers[col]] = item.text() if item else ""
                suppliers_data.append(supplier_data)

            # إنشاء البيانات النهائية
            export_data = {
                "export_info": {
                    "export_date": datetime.now().isoformat(),
                    "total_suppliers": len(suppliers_data),
                    "exported_by": "نظام إدارة الموردين"
                },
                "suppliers": suppliers_data
            }

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير إلى JSON: {str(e)}")





    def restore_suppliers_data(self):
        """استعادة بيانات الموردين من نسخة احتياطية"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QProgressDialog, QMessageBox
            from PyQt5.QtCore import QCoreApplication
            import json
            from datetime import datetime

            # تحذير المستخدم
            reply = QMessageBox.question(
                self,
                "⚠️ تحذير",
                "هذه العملية ستقوم بإضافة الموردين من النسخة الاحتياطية.\n"
                "الموردين الموجودين بنفس الاسم لن يتم تكرارهم.\n\n"
                "هل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # اختيار ملف النسخة الاحتياطية
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف النسخة الاحتياطية",
                "",
                "JSON Files (*.json);;All Files (*.*)"
            )

            if not file_path:
                return

            # شريط التقدم
            progress = QProgressDialog("جاري استعادة البيانات...", "إلغاء", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            try:
                # قراءة ملف النسخة الاحتياطية
                with open(file_path, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)

                if 'suppliers' not in backup_data:
                    show_error_message("خطأ", "ملف النسخة الاحتياطية غير صالح")
                    return

                suppliers_data = backup_data['suppliers']
                total_suppliers = len(suppliers_data)
                restored_count = 0
                skipped_count = 0
                errors = []

                progress.setValue(10)
                QCoreApplication.processEvents()

                # استعادة الموردين
                for i, supplier_data in enumerate(suppliers_data):
                    if progress.wasCanceled():
                        break

                    progress.setValue(10 + int((i / total_suppliers) * 80))
                    QCoreApplication.processEvents()

                    try:
                        # التحقق من عدم وجود مورد بنفس الاسم
                        existing = self.session.query(Supplier).filter_by(name=supplier_data['name']).first()
                        if existing:
                            skipped_count += 1
                            continue

                        # إنشاء مورد جديد
                        new_supplier = Supplier(
                            name=supplier_data['name'],
                            phone=supplier_data.get('phone'),
                            email=supplier_data.get('email'),
                            address=supplier_data.get('address'),
                            balance=supplier_data.get('balance', 0.0),
                            notes=supplier_data.get('notes'),
                            status=supplier_data.get('status', 'نشط')
                        )

                        # تحديد تاريخ الإنشاء إذا كان متوفراً
                        if supplier_data.get('created_at'):
                            try:
                                new_supplier.created_at = datetime.fromisoformat(supplier_data['created_at'])
                            except:
                                pass

                        self.session.add(new_supplier)
                        self.session.flush()  # للحصول على ID

                        # إضافة أرقام الهواتف الإضافية
                        if supplier_data.get('additional_phones'):
                            for phone_data in supplier_data['additional_phones']:
                                try:
                                    from database import SupplierPhone
                                    new_phone = SupplierPhone(
                                        supplier_id=new_supplier.id,
                                        phone_number=phone_data['phone_number'],
                                        label=phone_data.get('label'),
                                        is_primary=phone_data.get('is_primary', False)
                                    )
                                    self.session.add(new_phone)
                                except Exception as e:
                                    errors.append(f"خطأ في إضافة هاتف للمورد {supplier_data['name']}: {str(e)}")

                        restored_count += 1

                    except Exception as e:
                        errors.append(f"خطأ في استعادة المورد {supplier_data.get('name', 'غير معروف')}: {str(e)}")

                # حفظ التغييرات
                if restored_count > 0:
                    self.session.commit()
                    self.refresh_data()

                progress.setValue(100)
                progress.close()

                # عرض نتائج الاستعادة
                result_message = f"✅ تم استعادة البيانات!\n\n"
                result_message += f"📊 إجمالي الموردين في النسخة: {total_suppliers}\n"
                result_message += f"✅ تم استعادة: {restored_count} مورد\n"
                result_message += f"⏭️ تم تخطي: {skipped_count} مورد (موجود مسبقاً)\n"

                if errors:
                    result_message += f"\n⚠️ الأخطاء ({len(errors)}):\n"
                    result_message += "\n".join(errors[:5])  # أول 5 أخطاء
                    if len(errors) > 5:
                        result_message += f"\n... و {len(errors) - 5} خطأ آخر"

                show_info_message("نتائج الاستعادة", result_message)

            except Exception as e:
                progress.close()
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء قراءة ملف النسخة الاحتياطية: {str(e)}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}")





    # ==================== دوال الإحصائيات والتقارير ====================

    def show_statistics(self):
        """عرض الإحصائيات الأساسية للموردين"""
        try:
            suppliers = self.session.query(Supplier).all()

            # حساب الإحصائيات الأساسية
            total_suppliers = len(suppliers)
            active_suppliers = len([s for s in suppliers if s.status == 'نشط'])
            inactive_suppliers = len([s for s in suppliers if s.status == 'غير نشط'])

            positive_balance = len([s for s in suppliers if (s.balance or 0) > 0])
            negative_balance = len([s for s in suppliers if (s.balance or 0) < 0])
            zero_balance = len([s for s in suppliers if (s.balance or 0) == 0])

            total_balance = sum([s.balance or 0 for s in suppliers])

            # إحصائيات الاتصال
            with_phone = len([s for s in suppliers if s.phone])
            with_email = len([s for s in suppliers if s.email])
            with_address = len([s for s in suppliers if s.address])

            stats_text = f"""
📊 الإحصائيات الأساسية للموردين
═══════════════════════════════════════════════════════════════

👥 إجمالي الموردين: {total_suppliers}

📈 حالة الموردين:
─────────────────────────────────────────────────────────────
🟢 نشط: {active_suppliers} ({active_suppliers/total_suppliers*100:.1f}%)
🔴 غير نشط: {inactive_suppliers} ({inactive_suppliers/total_suppliers*100:.1f}%)

💰 توزيع الأرصدة:
─────────────────────────────────────────────────────────────
🟢 أرصدة موجبة: {positive_balance} ({positive_balance/total_suppliers*100:.1f}%)
🔴 أرصدة سالبة: {negative_balance} ({negative_balance/total_suppliers*100:.1f}%)
⚪ أرصدة صفر: {zero_balance} ({zero_balance/total_suppliers*100:.1f}%)
💵 إجمالي الأرصدة: {total_balance:,.2f} ريال

📞 معلومات الاتصال:
─────────────────────────────────────────────────────────────
📱 لديهم هاتف: {with_phone} ({with_phone/total_suppliers*100:.1f}%)
📧 لديهم إيميل: {with_email} ({with_email/total_suppliers*100:.1f}%)
📍 لديهم عنوان: {with_address} ({with_address/total_suppliers*100:.1f}%)

📅 تاريخ الإحصائيات: {QDate.currentDate().toString('yyyy-MM-dd')}
            """

            show_info_message("📊 الإحصائيات الأساسية", stats_text)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def show_detailed_statistics(self):
        """عرض الإحصائيات المفصلة للموردين"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
            from datetime import datetime, timedelta

            # إنشاء نافذة الإحصائيات المفصلة
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 الإحصائيات المفصلة للموردين")
            dialog.setFixedSize(900, 700)

            layout = QVBoxLayout()

            # منطقة النص
            text_area = QTextEdit()
            text_area.setReadOnly(True)
            text_area.setStyleSheet("""
                QTextEdit {
                    font-family: 'Courier New', monospace;
                    font-size: 11px;
                    background: white;
                    border: 2px solid #8b5cf6;
                    border-radius: 8px;
                    padding: 10px;
                }
            """)

            # حساب الإحصائيات المفصلة
            suppliers = self.session.query(Supplier).all()

            # إحصائيات زمنية
            now = datetime.now()
            this_month = len([s for s in suppliers if s.created_at and s.created_at.month == now.month and s.created_at.year == now.year])
            last_month = len([s for s in suppliers if s.created_at and s.created_at.month == (now.month-1 if now.month > 1 else 12) and s.created_at.year == (now.year if now.month > 1 else now.year-1)])
            this_year = len([s for s in suppliers if s.created_at and s.created_at.year == now.year])

            # إحصائيات الأرصدة المتقدمة
            balances = [s.balance or 0 for s in suppliers]
            max_balance = max(balances) if balances else 0
            min_balance = min(balances) if balances else 0
            avg_balance = sum(balances) / len(balances) if balances else 0

            # تصنيف الموردين حسب القيمة
            high_value = len([s for s in suppliers if (s.balance or 0) > 10000])
            medium_value = len([s for s in suppliers if 1000 <= (s.balance or 0) <= 10000])
            low_value = len([s for s in suppliers if (s.balance or 0) < 1000])

            stats_content = f"""
📊 الإحصائيات المفصلة للموردين
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {now.strftime('%Y-%m-%d %H:%M:%S')}

📈 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
👥 إجمالي الموردين: {len(suppliers)}
🟢 موردين نشطين: {len([s for s in suppliers if s.status == 'نشط'])}
🔴 موردين غير نشطين: {len([s for s in suppliers if s.status != 'نشط'])}

📅 الإحصائيات الزمنية:
─────────────────────────────────────────────────────────────────────────────
📊 هذا الشهر: {this_month} مورد
📊 الشهر الماضي: {last_month} مورد
📊 هذا العام: {this_year} مورد
📈 معدل النمو الشهري: {((this_month - last_month) / last_month * 100) if last_month > 0 else 0:+.1f}%

💰 تحليل الأرصدة المتقدم:
─────────────────────────────────────────────────────────────────────────────
💵 إجمالي الأرصدة: {sum(balances):,.2f} ريال
📈 أعلى رصيد: {max_balance:,.2f} ريال
📉 أقل رصيد: {min_balance:,.2f} ريال
📊 متوسط الرصيد: {avg_balance:,.2f} ريال

🏆 تصنيف الموردين حسب القيمة:
─────────────────────────────────────────────────────────────────────────────
💎 عالي القيمة (أكثر من 10,000): {high_value} ({high_value/len(suppliers)*100:.1f}%)
💰 متوسط القيمة (1,000 - 10,000): {medium_value} ({medium_value/len(suppliers)*100:.1f}%)
💸 منخفض القيمة (أقل من 1,000): {low_value} ({low_value/len(suppliers)*100:.1f}%)

📞 تحليل معلومات الاتصال:
─────────────────────────────────────────────────────────────────────────────
📱 لديهم هاتف: {len([s for s in suppliers if s.phone])} ({len([s for s in suppliers if s.phone])/len(suppliers)*100:.1f}%)
📧 لديهم إيميل: {len([s for s in suppliers if s.email])} ({len([s for s in suppliers if s.email])/len(suppliers)*100:.1f}%)
📍 لديهم عنوان: {len([s for s in suppliers if s.address])} ({len([s for s in suppliers if s.address])/len(suppliers)*100:.1f}%)
📝 لديهم ملاحظات: {len([s for s in suppliers if s.notes])} ({len([s for s in suppliers if s.notes])/len(suppliers)*100:.1f}%)

🔝 أعلى 10 موردين (حسب الرصيد):
─────────────────────────────────────────────────────────────────────────────
"""

            # أعلى 10 موردين
            top_suppliers = sorted(suppliers, key=lambda x: x.balance or 0, reverse=True)[:10]
            for i, supplier in enumerate(top_suppliers, 1):
                stats_content += f"{i:02d}. {supplier.name}: {supplier.balance or 0:,.2f} ريال\n"

            stats_content += f"""
🔻 أقل 10 موردين (حسب الرصيد):
─────────────────────────────────────────────────────────────────────────────
"""

            # أقل 10 موردين
            bottom_suppliers = sorted(suppliers, key=lambda x: x.balance or 0)[:10]
            for i, supplier in enumerate(bottom_suppliers, 1):
                stats_content += f"{i:02d}. {supplier.name}: {supplier.balance or 0:,.2f} ريال\n"

            stats_content += """
═══════════════════════════════════════════════════════════════════════════════
                                    نهاية الإحصائيات
═══════════════════════════════════════════════════════════════════════════════
"""

            text_area.setPlainText(stats_content)
            layout.addWidget(text_area)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("💾 حفظ الإحصائيات")
            save_btn.clicked.connect(lambda: self.save_report(stats_content, "إحصائيات_مفصلة_الموردين"))

            print_btn = QPushButton("🖨️ طباعة")
            print_btn.clicked.connect(lambda: self.print_report(text_area))

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(print_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات المفصلة: {str(e)}")

    def show_balance_analysis(self):
        """عرض تحليل الأرصدة - استخدام دالة التقرير المحسنة"""
        self.generate_balance_report()

    def show_monthly_report(self):
        """عرض التقرير الشهري - استخدام دالة التقرير المحسنة"""
        self.generate_monthly_report()


class AttachmentsDialog(QDialog):
    """نافذة إدارة المرفقات للموردين"""

    def __init__(self, parent, supplier, session):
        super().__init__(parent)
        self.supplier = supplier
        self.session = session
        self.attachments = []

        self.setWindowTitle(f"إدارة مرفقات المورد: {supplier.name}")
        self.setModal(True)
        self.resize(800, 600)

        self.init_ui()
        self.load_attachments()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # عنوان النافذة
        title_label = QLabel(f"📎 إدارة مرفقات المورد: {self.supplier.name}")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # قائمة المرفقات
        self.attachments_list = QListWidget()
        self.attachments_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background: #3498db;
                color: white;
            }
        """)
        layout.addWidget(self.attachments_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_file_btn = QPushButton("📁 إضافة ملف")
        add_file_btn.clicked.connect(self.add_file)
        add_file_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)

        view_file_btn = QPushButton("👁️ عرض")
        view_file_btn.clicked.connect(self.view_file)
        view_file_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5dade2;
            }
        """)

        delete_file_btn = QPushButton("🗑️ حذف")
        delete_file_btn.clicked.connect(self.delete_file)
        delete_file_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ec7063;
            }
        """)

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #bdc3c7;
            }
        """)

        buttons_layout.addWidget(add_file_btn)
        buttons_layout.addWidget(view_file_btn)
        buttons_layout.addWidget(delete_file_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def load_attachments(self):
        """تحميل قائمة المرفقات"""
        self.attachments_list.clear()

        # هنا يمكن إضافة كود لتحميل المرفقات من قاعدة البيانات
        # في الوقت الحالي سنعرض رسالة توضيحية
        item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد")
        item.setData(Qt.UserRole, None)
        self.attachments_list.addItem(item)

    def add_file(self):
        """إضافة ملف جديد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف لإضافته",
            "",
            "جميع الملفات (*.*)"
        )

        if file_path:
            file_name = os.path.basename(file_path)

            # إضافة الملف للقائمة
            item = QListWidgetItem(f"📄 {file_name}")
            item.setData(Qt.UserRole, file_path)
            self.attachments_list.addItem(item)

            # إزالة الرسالة التوضيحية إذا كانت موجودة
            if self.attachments_list.count() > 1:
                first_item = self.attachments_list.item(0)
                if first_item.data(Qt.UserRole) is None:
                    self.attachments_list.takeItem(0)

            show_info_message("تم", f"تم إضافة الملف: {file_name}")

    def view_file(self):
        """عرض الملف المحدد"""
        current_item = self.attachments_list.currentItem()
        if not current_item:
            show_error_message("خطأ", "الرجاء اختيار ملف من القائمة")
            return

        file_path = current_item.data(Qt.UserRole)
        if not file_path:
            show_error_message("خطأ", "لا يوجد ملف محدد")
            return

        try:
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', file_path])
            else:  # Linux
                subprocess.call(['xdg-open', file_path])

        except Exception as e:
            show_error_message("خطأ", f"لا يمكن فتح الملف: {str(e)}")

    def delete_file(self):
        """حذف الملف المحدد"""
        current_item = self.attachments_list.currentItem()
        if not current_item:
            show_error_message("خطأ", "الرجاء اختيار ملف من القائمة")
            return

        file_path = current_item.data(Qt.UserRole)
        if not file_path:
            show_error_message("خطأ", "لا يوجد ملف محدد")
            return

        if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا المرفق؟"):
            row = self.attachments_list.row(current_item)
            self.attachments_list.takeItem(row)

            # إضافة الرسالة التوضيحية إذا لم تعد هناك مرفقات
            if self.attachments_list.count() == 0:
                item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد")
                item.setData(Qt.UserRole, None)
                self.attachments_list.addItem(item)

            show_info_message("تم", "تم حذف المرفق بنجاح")

class SupplierBalanceAdjustmentDialog(QDialog):
    """نافذة حوار لتعديل المبلغ المستحق للمورد - مطابق للعملاء"""

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent)
        self.setWindowTitle("تعديل المبلغ المستحق")
        self.setModal(True)
        self.setMinimumSize(400, 300)
        self.supplier = supplier

        # تطبيق نمط أساسي للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
            }
            QLabel {
                color: #495057;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 12px;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عرض معلومات المورد
        supplier_info = QLabel(f"المورد: {self.supplier.name}")
        main_layout.addWidget(supplier_info)

        current_balance = QLabel(f"المبلغ المستحق الحالي: {self.supplier.balance:.2f}")
        main_layout.addWidget(current_balance)

        # إضافة شرح للمبلغ المستحق
        balance_explanation = QLabel("ملاحظة: القيمة الموجبة تعني أن المبلغ للمورد، والقيمة السالبة تعني أن المبلغ على المورد")
        balance_explanation.setWordWrap(True)
        main_layout.addWidget(balance_explanation)

        # إنشاء مجموعة الخيارات
        from PyQt5.QtWidgets import QGroupBox, QRadioButton, QButtonGroup, QFormLayout
        operation_group = QGroupBox("نوع العملية")
        operation_layout = QVBoxLayout()

        self.add_radio = QRadioButton("إضافة مبلغ")
        self.add_radio.setChecked(True)
        self.subtract_radio = QRadioButton("خصم مبلغ")
        self.set_radio = QRadioButton("تعيين قيمة جديدة")

        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.subtract_radio)
        operation_layout.addWidget(self.set_radio)

        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)

        # إنشاء مجموعة أزرار الاختيار
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.subtract_radio, 2)
        self.operation_group.addButton(self.set_radio, 3)

        # حقل إدخال المبلغ
        amount_layout = QFormLayout()
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("أدخل المبلغ")
        amount_layout.addRow("المبلغ:", self.amount_edit)
        main_layout.addLayout(amount_layout)

        # أزرار الحفظ والإلغاء باستخدام النمط الموحد
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        save_button = QPushButton("💾 تطبيق")
        save_button.clicked.connect(self.accept)
        save_button.setStyleSheet(UnifiedStyles.get_button_style("success", "normal"))

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(UnifiedStyles.get_button_style("secondary", "normal"))

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من أن حقل المبلغ ليس فارغاً
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            errors.append("يرجى إدخال المبلغ")
        else:
            # التحقق من صحة المبلغ
            try:
                amount = float(amount_text)
                if amount < 0:
                    errors.append("المبلغ يجب أن يكون صفر أو أكبر")
            except ValueError:
                errors.append("المبلغ يجب أن يكون رقماً صحيحاً")

        return errors

    def get_data(self):
        """الحصول على بيانات التعديل"""
        # التحقق من صحة البيانات أولاً
        errors = self.validate_data()
        if errors:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return None

        # تحويل النص إلى رقم بعد التأكد من صحته
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ")
            return None

        try:
            amount = float(amount_text)
        except ValueError:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ", "المبلغ المدخل غير صحيح")
            return None

        if amount < 0:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون صفر أو أكبر")
            return None

        operation_id = self.operation_group.checkedId()

        if operation_id == 1:
            # إضافة مبلغ
            return {'amount': amount, 'operation': 'add'}
        elif operation_id == 2:
            # خصم مبلغ
            return {'amount': amount, 'operation': 'subtract'}
        elif operation_id == 3:
            # تعيين قيمة جديدة
            current_balance = self.supplier.balance
            # حساب الفرق بين القيمة الحالية والقيمة الجديدة
            if amount == current_balance:
                # لا يوجد تغيير
                return {'amount': 0, 'operation': 'add'}
            elif amount > current_balance:
                return {'amount': amount - current_balance, 'operation': 'add'}
            else:
                return {'amount': current_balance - amount, 'operation': 'subtract'}

        return None
